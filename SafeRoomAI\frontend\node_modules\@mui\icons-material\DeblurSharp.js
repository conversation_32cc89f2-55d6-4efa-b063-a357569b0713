"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 3v18c4.97 0 9-4.03 9-9s-4.03-9-9-9"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "6",
  cy: "14",
  r: "1"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "6",
  cy: "18",
  r: "1"
}, "2"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "6",
  cy: "10",
  r: "1"
}, "3"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "3",
  cy: "10",
  r: ".5"
}, "4"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "6",
  cy: "6",
  r: "1"
}, "5"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "3",
  cy: "14",
  r: ".5"
}, "6"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "21",
  r: ".5"
}, "7"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "3",
  r: ".5"
}, "8"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "6",
  r: "1"
}, "9"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "14",
  r: "1.5"
}, "10"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "10",
  r: "1.5"
}, "11"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "18",
  r: "1"
}, "12")], 'DeblurSharp');