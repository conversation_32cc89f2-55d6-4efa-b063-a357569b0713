"""
Individual Agent Definitions for AutoGen Code Review Team
"""

import autogen
from config import AutoGenConfig

class CodeReviewAgents:
    """Factory class for creating specialized code review agents"""
    
    def __init__(self):
        self.config = AutoGenConfig()
        self.llm_config = {
            "model": self.config.LLM_MODEL,
            "api_key": self.config.OPENAI_API_KEY,
            "temperature": self.config.TEMPERATURE,
            "max_tokens": self.config.MAX_TOKENS,
        }
    
    def create_senior_developer(self) -> autogen.AssistantAgent:
        """Create Senior Developer Agent"""
        agent_config = self.config.AGENTS_CONFIG["senior_developer"]
        
        return autogen.AssistantAgent(
            name=agent_config["name"],
            system_message=agent_config["system_message"],
            llm_config=self.llm_config,
            max_consecutive_auto_reply=self.config.MAX_CONSECUTIVE_AUTO_REPLY,
            human_input_mode=self.config.HUMAN_INPUT_MODE,
            code_execution_config=self.config.CODE_EXECUTION_CONFIG,
        )
    
    def create_security_expert(self) -> autogen.AssistantAgent:
        """Create Security Expert Agent"""
        agent_config = self.config.AGENTS_CONFIG["security_expert"]
        
        return autogen.AssistantAgent(
            name=agent_config["name"],
            system_message=agent_config["system_message"],
            llm_config=self.llm_config,
            max_consecutive_auto_reply=self.config.MAX_CONSECUTIVE_AUTO_REPLY,
            human_input_mode=self.config.HUMAN_INPUT_MODE,
            code_execution_config=self.config.CODE_EXECUTION_CONFIG,
        )
    
    def create_performance_analyst(self) -> autogen.AssistantAgent:
        """Create Performance Analyst Agent"""
        agent_config = self.config.AGENTS_CONFIG["performance_analyst"]
        
        return autogen.AssistantAgent(
            name=agent_config["name"],
            system_message=agent_config["system_message"],
            llm_config=self.llm_config,
            max_consecutive_auto_reply=self.config.MAX_CONSECUTIVE_AUTO_REPLY,
            human_input_mode=self.config.HUMAN_INPUT_MODE,
            code_execution_config=self.config.CODE_EXECUTION_CONFIG,
        )
    
    def create_documentation_agent(self) -> autogen.AssistantAgent:
        """Create Documentation Specialist Agent"""
        agent_config = self.config.AGENTS_CONFIG["documentation_agent"]
        
        return autogen.AssistantAgent(
            name=agent_config["name"],
            system_message=agent_config["system_message"],
            llm_config=self.llm_config,
            max_consecutive_auto_reply=self.config.MAX_CONSECUTIVE_AUTO_REPLY,
            human_input_mode=self.config.HUMAN_INPUT_MODE,
            code_execution_config=self.config.CODE_EXECUTION_CONFIG,
        )
    
    def create_team_lead(self) -> autogen.AssistantAgent:
        """Create Team Lead Agent"""
        agent_config = self.config.AGENTS_CONFIG["team_lead"]
        
        return autogen.AssistantAgent(
            name=agent_config["name"],
            system_message=agent_config["system_message"],
            llm_config=self.llm_config,
            max_consecutive_auto_reply=self.config.MAX_CONSECUTIVE_AUTO_REPLY,
            human_input_mode=self.config.HUMAN_INPUT_MODE,
            code_execution_config=self.config.CODE_EXECUTION_CONFIG,
        )
    
    def create_user_proxy(self) -> autogen.UserProxyAgent:
        """Create User Proxy Agent for initiating conversations"""
        return autogen.UserProxyAgent(
            name="Code_Reviewer",
            system_message="You are initiating a code review process. Submit code to the team for analysis.",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=0,
            code_execution_config=False,
        )
    
    def create_all_agents(self) -> dict:
        """Create all agents and return as dictionary"""
        return {
            "senior_developer": self.create_senior_developer(),
            "security_expert": self.create_security_expert(),
            "performance_analyst": self.create_performance_analyst(),
            "documentation_agent": self.create_documentation_agent(),
            "team_lead": self.create_team_lead(),
            "user_proxy": self.create_user_proxy()
        }

class ConversationManager:
    """Manages conversation flow between agents"""
    
    def __init__(self, agents: dict):
        self.agents = agents
        self.config = AutoGenConfig()
    
    def create_group_chat(self) -> autogen.GroupChat:
        """Create group chat with all agents"""
        # Define the order of speakers for structured review
        agent_list = [
            self.agents["user_proxy"],
            self.agents["senior_developer"],
            self.agents["security_expert"],
            self.agents["performance_analyst"],
            self.agents["documentation_agent"],
            self.agents["team_lead"]
        ]
        
        return autogen.GroupChat(
            agents=agent_list,
            messages=[],
            max_round=self.config.REVIEW_PROCESS["max_total_rounds"],
            speaker_selection_method="round_robin",  # Structured conversation flow
        )
    
    def create_group_chat_manager(self, group_chat: autogen.GroupChat) -> autogen.GroupChatManager:
        """Create group chat manager"""
        return autogen.GroupChatManager(
            groupchat=group_chat,
            llm_config={
                "model": self.config.LLM_MODEL,
                "api_key": self.config.OPENAI_API_KEY,
                "temperature": self.config.TEMPERATURE,
            }
        )
    
    def format_code_for_review(self, code: str, language: str = "python") -> str:
        """Format code submission message"""
        return self.config.INITIAL_REVIEW_PROMPT.format(
            language=language,
            code=code
        )
    
    def initiate_review(self, code: str, language: str = "python") -> str:
        """Initiate code review process"""
        # Create group chat
        group_chat = self.create_group_chat()
        manager = self.create_group_chat_manager(group_chat)
        
        # Format initial message
        initial_message = self.format_code_for_review(code, language)
        
        # Start the conversation
        try:
            self.agents["user_proxy"].initiate_chat(
                manager,
                message=initial_message
            )
            
            # Extract conversation history
            conversation_history = []
            for message in group_chat.messages:
                conversation_history.append({
                    "speaker": message.get("name", "Unknown"),
                    "content": message.get("content", ""),
                    "role": message.get("role", "assistant")
                })
            
            return conversation_history
            
        except Exception as e:
            return [{"speaker": "System", "content": f"Error during review: {str(e)}", "role": "system"}]

# Utility functions for agent management
def get_agent_descriptions() -> dict:
    """Get descriptions of all agents"""
    return {
        "Senior Developer": "🧑‍💻 Reviews architecture, design patterns, and code structure",
        "Security Expert": "🔒 Identifies security vulnerabilities and secure coding practices", 
        "Performance Analyst": "⚡ Analyzes performance bottlenecks and optimization opportunities",
        "Documentation Specialist": "📝 Evaluates code readability and documentation quality",
        "Team Lead": "👨‍💼 Coordinates review process and provides final recommendations"
    }

def get_review_categories() -> list:
    """Get list of review categories"""
    return [
        "Architecture & Design",
        "Security Vulnerabilities", 
        "Performance Optimization",
        "Code Documentation",
        "Best Practices",
        "Maintainability"
    ]
