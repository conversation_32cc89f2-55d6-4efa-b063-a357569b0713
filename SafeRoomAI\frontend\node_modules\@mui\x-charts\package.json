{"name": "@mui/x-charts", "version": "7.29.1", "description": "The community edition of the Charts components (MUI X).", "author": "MUI Team", "main": "./node/index.js", "license": "MIT", "bugs": {"url": "https://github.com/mui/mui-x/issues"}, "homepage": "https://mui.com/x/react-charts/", "sideEffects": false, "publishConfig": {"access": "public", "directory": "build"}, "keywords": ["react", "react-component", "mui", "mui-x", "material-ui", "material design", "charts"], "repository": {"type": "git", "url": "git+https://github.com/mui/mui-x.git", "directory": "packages/x-charts"}, "dependencies": {"@babel/runtime": "^7.25.7", "@mui/utils": "^5.16.6 || ^6.0.0 || ^7.0.0", "@react-spring/rafz": "^9.7.5", "@react-spring/web": "^9.7.5", "clsx": "^2.1.1", "prop-types": "^15.8.1", "@mui/x-charts-vendor": "7.20.0", "@mui/x-internals": "7.29.0"}, "peerDependencies": {"@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@mui/material": "^5.15.14 || ^6.0.0 || ^7.0.0", "@mui/system": "^5.15.14 || ^6.0.0 || ^7.0.0", "react": "^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^17.0.0 || ^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@emotion/react": {"optional": true}, "@emotion/styled": {"optional": true}}, "engines": {"node": ">=14.0.0"}, "private": false, "module": "./index.js", "types": "./index.d.ts"}