export interface DialogClasses {
    /** Styles applied to the root element. */
    root: string;
    /** Styles applied to the container element if `scroll="paper"`. */
    scrollPaper: string;
    /** Styles applied to the container element if `scroll="body"`. */
    scrollBody: string;
    /** Styles applied to the container element. */
    container: string;
    /** Styles applied to the Paper component. */
    paper: string;
    /** Styles applied to the Paper component if `scroll="paper"`.
     * @deprecated Combine the [.MuiDialog-paper](/material-ui/api/dialog/#dialog-classes-paper) and [.MuiDialog-scrollPaper](/material-ui/api/dialog/#dialog-classes-scrollPaper) classes instead. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
     */
    paperScrollPaper: string;
    /** Styles applied to the Paper component if `scroll="body"`.
     * @deprecated Combine the [.MuiDialog-paper](/material-ui/api/dialog/#dialog-classes-paper) and [.MuiDialog-scrollBody](/material-ui/api/dialog/#dialog-classes-scrollBody) classes instead. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.
     */
    paperScrollBody: string;
    /** Styles applied to the Paper component if `maxWidth=false`. */
    paperWidthFalse: string;
    /** Styles applied to the Paper component if `maxWidth="xs"`. */
    paperWidthXs: string;
    /** Styles applied to the Paper component if `maxWidth="sm"`. */
    paperWidthSm: string;
    /** Styles applied to the Paper component if `maxWidth="md"`. */
    paperWidthMd: string;
    /** Styles applied to the Paper component if `maxWidth="lg"`. */
    paperWidthLg: string;
    /** Styles applied to the Paper component if `maxWidth="xl"`. */
    paperWidthXl: string;
    /** Styles applied to the Paper component if `fullWidth={true}`. */
    paperFullWidth: string;
    /** Styles applied to the Paper component if `fullScreen={true}`. */
    paperFullScreen: string;
}
export type DialogClassKey = keyof DialogClasses;
export declare function getDialogUtilityClass(slot: string): string;
declare const dialogClasses: DialogClasses;
export default dialogClasses;
