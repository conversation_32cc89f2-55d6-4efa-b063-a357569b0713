# AI Agent Frameworks Assignment

## Overview
This project demonstrates three different AI agent implementations using different frameworks:

1. **Lang<PERSON>hain Agent**: Research Paper Analyzer
2. **AutoGen Agent**: Code Review Team  
3. **LlamaIndex Agent**: Customer Support Knowledge Base

## Project Structure
```
ai_agent_assignment/
├── langchain_agent/          # Task 1: Research Paper Analyzer
├── autogen_agent/           # Task 2: Code Review Team
├── llamaindex_agent/        # Task 3: Customer Support KB
├── comparison_report.md     # Framework comparison
├── requirements.txt         # Dependencies
└── README.md               # This file
```

## Setup Instructions

### Prerequisites
- Python 3.8+
- OpenAI API key (or other LLM provider)
- Virtual environment recommended

### Installation
```bash
# Create virtual environment
python -m venv ai_agents_env
source ai_agents_env/bin/activate  # On Windows: ai_agents_env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Environment Variables
Create a `.env` file in the root directory:
```
OPENAI_API_KEY=your_openai_api_key_here
```

## Agent Descriptions

### 1. LangChain Research Paper Analyzer
- **Purpose**: Analyze academic research papers and extract insights
- **Features**: PDF processing, citation extraction, research gap identification
- **Framework**: LangChain with RAG implementation

### 2. AutoGen Code Review Team
- **Purpose**: Multi-agent code review system
- **Features**: Architecture review, security analysis, performance optimization
- **Framework**: AutoGen with role-based agent collaboration

### 3. LlamaIndex Customer Support KB
- **Purpose**: Intelligent customer service assistant
- **Features**: Knowledge base search, ticket routing, solution recommendations
- **Framework**: LlamaIndex with advanced indexing

## Running the Agents

Each agent has its own directory with specific instructions. See individual README files in each subdirectory.

## Demo Video
[Link to 20-minute demonstration video will be added]

## Author
[Your Name]
[Course Information]
[Date]
