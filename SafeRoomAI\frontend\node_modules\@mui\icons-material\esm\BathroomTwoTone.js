"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M4 20h16V4H4zm5-2c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m3 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m3 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m0-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m-8-4c0-2.76 2.24-5 5-5s5 2.24 5 5v1H7z",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("circle", {
  cx: "15",
  cy: "14",
  r: "1"
}, "1"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "14",
  r: "1"
}, "2"), /*#__PURE__*/_jsx("circle", {
  cx: "15",
  cy: "17",
  r: "1"
}, "3"), /*#__PURE__*/_jsx("path", {
  d: "M20 2H4c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2m0 18H4V4h16z"
}, "4"), /*#__PURE__*/_jsx("path", {
  d: "M17 11c0-2.76-2.24-5-5-5s-5 2.24-5 5v1h10zm-8.46-.5c.24-1.69 1.7-3 3.46-3s3.22 1.31 3.47 3z"
}, "5"), /*#__PURE__*/_jsx("circle", {
  cx: "9",
  cy: "17",
  r: "1"
}, "6"), /*#__PURE__*/_jsx("circle", {
  cx: "9",
  cy: "14",
  r: "1"
}, "7"), /*#__PURE__*/_jsx("circle", {
  cx: "12",
  cy: "17",
  r: "1"
}, "8")], 'BathroomTwoTone');