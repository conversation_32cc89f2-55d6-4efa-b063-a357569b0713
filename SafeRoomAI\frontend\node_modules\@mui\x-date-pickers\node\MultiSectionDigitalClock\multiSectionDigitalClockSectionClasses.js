"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.getMultiSectionDigitalClockSectionUtilityClass = getMultiSectionDigitalClockSectionUtilityClass;
exports.multiSectionDigitalClockSectionClasses = void 0;
var _generateUtilityClass = _interopRequireDefault(require("@mui/utils/generateUtilityClass"));
var _generateUtilityClasses = _interopRequireDefault(require("@mui/utils/generateUtilityClasses"));
function getMultiSectionDigitalClockSectionUtilityClass(slot) {
  return (0, _generateUtilityClass.default)('MuiMultiSectionDigitalClockSection', slot);
}
const multiSectionDigitalClockSectionClasses = exports.multiSectionDigitalClockSectionClasses = (0, _generateUtilityClasses.default)('MuiMultiSectionDigitalClockSection', ['root', 'item']);