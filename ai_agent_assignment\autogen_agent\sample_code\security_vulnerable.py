"""
Sample code with security vulnerabilities for testing the AutoGen Code Review Team
"""

import os
import subprocess
import sqlite3
from flask import Flask, request, render_template_string

app = Flask(__name__)

# SECURITY ISSUE: Hardcoded credentials
DATABASE_PASSWORD = "admin123"
API_KEY = "sk-1234567890abcdef"

def execute_user_command(user_input):
    """
    SECURITY ISSUE: Command injection vulnerability
    User input is executed directly without validation
    """
    result = subprocess.run(user_input, shell=True, capture_output=True, text=True)
    return result.stdout

def get_user_data(user_id):
    """
    SECURITY ISSUE: SQL injection vulnerability
    User input is concatenated directly into SQL query
    """
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    # Vulnerable SQL query
    query = f"SELECT * FROM users WHERE id = {user_id}"
    cursor.execute(query)
    
    result = cursor.fetchall()
    conn.close()
    return result

def read_user_file(filename):
    """
    SECURITY ISSUE: Path traversal vulnerability
    No validation of file path allows directory traversal
    """
    file_path = f"/uploads/{filename}"
    
    try:
        with open(file_path, 'r') as f:
            return f.read()
    except FileNotFoundError:
        return "File not found"

@app.route('/search')
def search():
    """
    SECURITY ISSUE: XSS vulnerability
    User input is rendered directly without escaping
    """
    query = request.args.get('q', '')
    
    # Vulnerable template rendering
    template = f"""
    <html>
        <body>
            <h1>Search Results for: {query}</h1>
            <p>No results found for your search.</p>
        </body>
    </html>
    """
    
    return render_template_string(template)

def authenticate_user(username, password):
    """
    SECURITY ISSUE: Weak authentication
    No password hashing, simple comparison
    """
    # Hardcoded admin credentials
    if username == "admin" and password == "password123":
        return True
    
    # Check against database (vulnerable query)
    conn = sqlite3.connect('users.db')
    cursor = conn.cursor()
    
    query = f"SELECT * FROM users WHERE username = '{username}' AND password = '{password}'"
    cursor.execute(query)
    
    result = cursor.fetchone()
    conn.close()
    
    return result is not None

def process_payment(card_number, amount):
    """
    SECURITY ISSUE: Sensitive data logging
    Credit card information is logged in plain text
    """
    print(f"Processing payment: Card {card_number}, Amount ${amount}")
    
    # SECURITY ISSUE: No input validation
    if len(card_number) != 16:
        return False
    
    # Simulate payment processing
    return True

def generate_session_token():
    """
    SECURITY ISSUE: Weak random number generation
    Using predictable random number generation for security tokens
    """
    import random
    
    # Weak random token generation
    token = ""
    for i in range(32):
        token += str(random.randint(0, 9))
    
    return token

class UserManager:
    """
    SECURITY ISSUE: Insecure direct object references
    No authorization checks for user data access
    """
    
    def __init__(self):
        self.users = {}
    
    def get_user_profile(self, user_id):
        """No authorization check - any user can access any profile"""
        return self.users.get(user_id, {})
    
    def update_user_profile(self, user_id, data):
        """No authorization check - any user can update any profile"""
        if user_id in self.users:
            self.users[user_id].update(data)
            return True
        return False
    
    def delete_user(self, user_id):
        """No authorization check - any user can delete any account"""
        if user_id in self.users:
            del self.users[user_id]
            return True
        return False

# SECURITY ISSUE: Debug mode enabled in production
if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0')  # Exposes debug info to all hosts
