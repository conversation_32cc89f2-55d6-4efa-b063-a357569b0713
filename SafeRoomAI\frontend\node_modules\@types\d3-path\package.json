{"name": "@types/d3-path", "version": "3.1.1", "description": "TypeScript definitions for d3-path", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/d3-path", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/tomwanzek"}, {"name": "<PERSON>", "githubUsername": "gust<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gustavderdrache"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "githubUsername": "Methuselah96", "url": "https://github.com/Methuselah96"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/d3-path"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "a49fc946781c1138c0dd932ed2a34ea60587bcc0b39790e4eb9a51cb32aaa90b", "typeScriptVersion": "5.0"}