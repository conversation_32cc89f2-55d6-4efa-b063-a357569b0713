'use client';

import * as React from 'react';
import { useCartesianContext } from "../context/CartesianProvider/index.js";
import { ZAxisContext } from "../context/ZAxisContextProvider.js";
export function useXColorScale(identifier) {
  const {
    xAxis,
    xAxisIds
  } = useCartesianContext();
  const id = typeof identifier === 'string' ? identifier : xAxisIds[identifier ?? 0];
  return xAxis[id].colorScale;
}
export function useYColorScale(identifier) {
  const {
    yAxis,
    yAxisIds
  } = useCartesianContext();
  const id = typeof identifier === 'string' ? identifier : yAxisIds[identifier ?? 0];
  return yAxis[id].colorScale;
}
export function useZColorScale(identifier) {
  const {
    zAxis,
    zAxisIds
  } = React.useContext(ZAxisContext);
  const id = typeof identifier === 'string' ? identifier : zAxisIds[identifier ?? 0];
  return zAxis[id]?.colorScale;
}