"""
Configuration settings for the LlamaIndex Customer Support Agent
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class LlamaIndexConfig:
    """Configuration class for the LlamaIndex Customer Support Agent"""
    
    # API Keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Model Settings
    LLM_MODEL = "gpt-3.5-turbo"
    EMBEDDING_MODEL = "text-embedding-ada-002"
    TEMPERATURE = 0.1  # Low temperature for consistent support responses
    MAX_TOKENS = 1000
    
    # LlamaIndex Settings
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    SIMILARITY_TOP_K = 5  # Number of similar chunks to retrieve
    
    # Vector Store Settings
    VECTOR_STORE_TYPE = "faiss"  # Options: faiss, chroma, pinecone
    PERSIST_DIR = "./vector_store"
    
    # Document Processing Settings
    SUPPORTED_FORMATS = ['.pdf', '.txt', '.md', '.docx']
    MAX_FILE_SIZE_MB = 50
    
    # Knowledge Base Structure
    KNOWLEDGE_BASE_PATH = "./knowledge_base"
    CATEGORIES = {
        "product_manuals": {
            "description": "Product documentation and user guides",
            "priority": 1
        },
        "faqs": {
            "description": "Frequently asked questions",
            "priority": 2
        },
        "policies": {
            "description": "Company policies and terms",
            "priority": 3
        },
        "support_articles": {
            "description": "Technical support articles and guides",
            "priority": 1
        },
        "troubleshooting": {
            "description": "Problem resolution guides",
            "priority": 1
        }
    }
    
    # Response Generation Settings
    RESPONSE_CONFIG = {
        "include_sources": True,
        "max_sources": 3,
        "confidence_threshold": 0.7,
        "fallback_response": "I don't have specific information about that in my knowledge base. Please contact our support team for personalized assistance."
    }
    
    # Query Processing Prompts
    SYSTEM_PROMPT = """You are a helpful customer support agent with access to a comprehensive knowledge base.
    Your role is to provide accurate, helpful, and friendly responses to customer inquiries.
    
    Guidelines:
    1. Always be polite and professional
    2. Provide specific, actionable information when possible
    3. If you're not certain about something, say so
    4. Reference the source documents when providing information
    5. If the query is outside your knowledge base, politely direct them to human support
    6. Keep responses concise but complete
    7. Use a friendly, conversational tone
    
    When answering questions:
    - Start with a direct answer to their question
    - Provide step-by-step instructions when applicable
    - Include relevant warnings or important notes
    - Suggest related resources if helpful
    """
    
    QUERY_PROMPT_TEMPLATE = """Based on the following context from our knowledge base, please answer the customer's question.
    
    Context:
    {context}
    
    Customer Question: {question}
    
    Please provide a helpful, accurate response based on the context provided. If the context doesn't contain enough information to fully answer the question, say so and suggest contacting support for more help.
    
    Response:"""
    
    # Metadata Enhancement
    METADATA_FIELDS = {
        "category": "Document category (manual, faq, policy, etc.)",
        "version": "Document version number",
        "last_updated": "Last modification date",
        "author": "Document author or department",
        "tags": "Relevant tags for categorization",
        "priority": "Priority level for search ranking"
    }
    
    # Search Configuration
    SEARCH_CONFIG = {
        "hybrid_search": True,  # Combine keyword and semantic search
        "keyword_weight": 0.3,
        "semantic_weight": 0.7,
        "rerank_results": True,
        "diversity_threshold": 0.8  # Avoid too similar results
    }
    
    # Logging Configuration
    LOGGING_CONFIG = {
        "log_queries": True,
        "log_responses": True,
        "log_sources": True,
        "log_performance": True,
        "log_file": "./logs/support_agent.log"
    }
    
    # Performance Monitoring
    PERFORMANCE_CONFIG = {
        "track_response_time": True,
        "track_accuracy": True,
        "track_user_satisfaction": True,
        "analytics_file": "./analytics/performance_metrics.json"
    }

# Validation
if not LlamaIndexConfig.OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable is required")

# Create necessary directories
directories_to_create = [
    LlamaIndexConfig.KNOWLEDGE_BASE_PATH,
    LlamaIndexConfig.PERSIST_DIR,
    "./logs",
    "./analytics"
]

for category in LlamaIndexConfig.CATEGORIES.keys():
    directories_to_create.append(f"{LlamaIndexConfig.KNOWLEDGE_BASE_PATH}/{category}")

for directory in directories_to_create:
    os.makedirs(directory, exist_ok=True)
