import type { BarLabelOwnerState } from './BarLabel.types';
export interface BarLabelClasses {
    /** Styles applied to the root element. */
    root: string;
    /** Styles applied to the root element if it is highlighted. */
    highlighted: string;
    /** Styles applied to the root element if it is faded. */
    faded: string;
}
export type BarLabelClassKey = keyof BarLabelClasses;
export declare function getBarLabelUtilityClass(slot: string): string;
export declare const barLabelClasses: Record<"highlighted" | "faded" | "root", string>;
export declare const useUtilityClasses: (ownerState: BarLabelOwnerState) => Record<"root", string>;
