import * as React from 'react';
import { SpringValue } from '@react-spring/web';
import { PieItemId } from '../models';
import { HighlightScope } from '../context';
export interface PieArcClasses {
    /** Styles applied to the root element. */
    root: string;
    /** Styles applied to the root element when highlighted. */
    highlighted: string;
    /** Styles applied to the root element when faded. */
    faded: string;
}
export type PieArcClassKey = keyof PieArcClasses;
interface PieArcOwnerState {
    id: PieItemId;
    dataIndex: number;
    color: string;
    isFaded: boolean;
    isHighlighted: boolean;
    classes?: Partial<PieArcClasses>;
}
export declare function getPieArcUtilityClass(slot: string): string;
export declare const pieArcClasses: PieArcClasses;
export type PieArcProps = Omit<React.SVGProps<SVGPathElement>, 'ref' | 'id'> & PieArcOwnerState & {
    cornerRadius: SpringValue<number>;
    endAngle: SpringValue<number>;
    /**
     * @deprecated Use the `isFaded` or `isHighlighted` props instead.
     */
    highlightScope?: Partial<HighlightScope>;
    innerRadius: SpringValue<number>;
    onClick?: (event: React.MouseEvent<SVGPathElement, MouseEvent>) => void;
    outerRadius: SpringValue<number>;
    paddingAngle: SpringValue<number>;
    startAngle: SpringValue<number>;
};
declare function PieArc(props: PieArcProps): React.JSX.Element;
declare namespace PieArc {
    var propTypes: any;
}
export { PieArc };
