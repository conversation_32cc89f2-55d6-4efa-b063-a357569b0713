"""
Streamlit Web Interface for AutoGen Code Review Team Agent
"""

import streamlit as st
import json
from datetime import datetime
import tempfile
import os

# Local imports
from code_review_team import CodeReviewTeam
from agents import get_agent_descriptions, get_review_categories

# Page configuration
st.set_page_config(
    page_title="Code Review Team",
    page_icon="👥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'review_team' not in st.session_state:
    st.session_state.review_team = CodeReviewTeam()
if 'current_review' not in st.session_state:
    st.session_state.current_review = None
if 'review_history' not in st.session_state:
    st.session_state.review_history = []

def main():
    """Main Streamlit application"""
    
    # Title and description
    st.title("👥 AutoGen Code Review Team")
    st.markdown("**Multi-agent AI code review system powered by AutoGen**")
    
    # Sidebar for team info and controls
    with st.sidebar:
        st.header("🤖 Review Team")
        
        # Team member descriptions
        team_info = st.session_state.review_team.get_team_info()
        team_members = team_info["team_members"]
        
        for role, description in team_members.items():
            st.write(f"**{role}**")
            st.write(description)
            st.write("---")
        
        # Review statistics
        st.header("📊 Statistics")
        history = st.session_state.review_team.get_review_history()
        st.metric("Total Reviews", len(history))
        
        if history:
            avg_score = sum(r.get("overall_score", 0) for r in history) / len(history)
            st.metric("Average Score", f"{avg_score:.1f}/10")
    
    # Main content area
    tab1, tab2, tab3, tab4 = st.tabs([
        "🔍 Code Review", 
        "📊 Review Results", 
        "📈 Team Discussion", 
        "📋 Review History"
    ])
    
    with tab1:
        st.header("🔍 Submit Code for Review")
        
        # Code input section
        col1, col2 = st.columns([3, 1])
        
        with col1:
            # Language selection
            language = st.selectbox(
                "Programming Language",
                ["python", "javascript", "java", "cpp", "csharp", "go", "rust", "other"],
                help="Select the programming language of your code"
            )
            
            # Code description
            description = st.text_input(
                "Code Description (Optional)",
                placeholder="Brief description of what this code does...",
                help="Provide context about the code functionality"
            )
        
        with col2:
            st.write("**Sample Code**")
            if st.button("🐍 Python Example"):
                st.session_state.sample_code = get_python_sample()
            if st.button("🌐 JavaScript Example"):
                st.session_state.sample_code = get_javascript_sample()
            if st.button("🔒 Security Issue Example"):
                st.session_state.sample_code = get_security_sample()
        
        # Code input
        code_input = st.text_area(
            "Code to Review",
            value=st.session_state.get('sample_code', ''),
            height=300,
            placeholder="Paste your code here for review...",
            help="Enter the code you want the team to review"
        )
        
        # Review options
        col1, col2, col3 = st.columns(3)
        
        with col1:
            focus_areas = st.multiselect(
                "Focus Areas",
                get_review_categories(),
                default=get_review_categories(),
                help="Select specific areas to focus on during review"
            )
        
        with col2:
            review_depth = st.selectbox(
                "Review Depth",
                ["Quick", "Standard", "Comprehensive"],
                index=1,
                help="Choose the depth of analysis"
            )
        
        with col3:
            save_report = st.checkbox(
                "Save Report",
                value=True,
                help="Save the review report to file"
            )
        
        # Submit for review
        if st.button("🚀 Start Code Review", type="primary", disabled=not code_input.strip()):
            with st.spinner("🤖 Team is reviewing your code... This may take a few minutes."):
                try:
                    # Conduct the review
                    review_result = st.session_state.review_team.review_code(
                        code=code_input,
                        language=language,
                        description=description
                    )
                    
                    # Store the result
                    st.session_state.current_review = review_result
                    st.session_state.review_history.append(review_result)
                    
                    # Save report if requested
                    if save_report and review_result.get("success"):
                        filename = st.session_state.review_team.save_review_report(review_result)
                        st.success(f"✅ Review completed! Report saved to {filename}")
                    else:
                        st.success("✅ Review completed!")
                    
                    # Switch to results tab
                    st.rerun()
                    
                except Exception as e:
                    st.error(f"❌ Review failed: {str(e)}")
    
    with tab2:
        st.header("📊 Review Results")
        
        if st.session_state.current_review:
            review = st.session_state.current_review
            
            if review.get("success"):
                # Overview metrics
                st.subheader("📈 Review Overview")
                
                col1, col2, col3, col4 = st.columns(4)
                metrics = review.get("metrics", {})
                
                with col1:
                    st.metric("Overall Score", f"{metrics.get('overall_score', 0)}/10")
                with col2:
                    st.metric("Total Issues", metrics.get('total_issues', 0))
                with col3:
                    st.metric("Review Quality", metrics.get('review_quality', 'Unknown'))
                with col4:
                    st.metric("Duration", f"{review.get('duration_seconds', 0):.1f}s")
                
                # Issues breakdown
                st.subheader("🚨 Issues Identified")
                issues = review.get("issues", [])
                
                if issues:
                    # Group issues by severity
                    severity_groups = {}
                    for issue in issues:
                        severity = issue.get("severity", "Unknown")
                        if severity not in severity_groups:
                            severity_groups[severity] = []
                        severity_groups[severity].append(issue)
                    
                    # Display issues by severity
                    severity_colors = {
                        "Critical": "🔴",
                        "High": "🟠", 
                        "Medium": "🟡",
                        "Low": "🟢"
                    }
                    
                    for severity in ["Critical", "High", "Medium", "Low"]:
                        if severity in severity_groups:
                            st.write(f"### {severity_colors.get(severity, '⚪')} {severity} Issues")
                            for issue in severity_groups[severity]:
                                with st.expander(f"{issue.get('category', 'General')} - {issue.get('agent', 'Unknown')}"):
                                    st.write(issue.get('description', 'No description'))
                else:
                    st.success("🎉 No issues identified! Great code quality.")
                
                # Recommendations
                st.subheader("💡 Recommendations")
                recommendations = review.get("recommendations", [])
                
                if recommendations:
                    for i, rec in enumerate(recommendations):
                        with st.expander(f"Recommendation {i+1}: {rec.get('category', 'General')}"):
                            st.write(f"**From:** {rec.get('agent', 'Unknown')}")
                            st.write(rec.get('recommendation', 'No recommendation'))
                else:
                    st.info("No specific recommendations provided.")
                
                # Final summary
                if review.get("final_summary"):
                    st.subheader("📋 Team Lead Summary")
                    st.write(review["final_summary"])
            
            else:
                st.error(f"❌ Review failed: {review.get('error', 'Unknown error')}")
        
        else:
            st.info("👆 Submit code for review to see results here.")
    
    with tab3:
        st.header("📈 Team Discussion")
        
        if st.session_state.current_review and st.session_state.current_review.get("success"):
            review = st.session_state.current_review
            
            # Agent feedback section
            st.subheader("🤖 Individual Agent Feedback")
            agent_feedback = review.get("agent_feedback", {})
            
            for agent, feedback_list in agent_feedback.items():
                with st.expander(f"{agent} Analysis"):
                    for i, feedback in enumerate(feedback_list):
                        st.write(f"**Analysis {i+1}:**")
                        st.write(feedback)
                        if i < len(feedback_list) - 1:
                            st.write("---")
            
            # Team discussion
            st.subheader("💬 Team Discussion")
            team_discussion = review.get("team_discussion", [])
            
            if team_discussion:
                for message in team_discussion:
                    speaker = message.get("speaker", "Unknown")
                    content = message.get("content", "")
                    
                    with st.chat_message(speaker.lower()):
                        st.write(f"**{speaker}:**")
                        st.write(content)
            else:
                st.info("No team discussion recorded.")
            
            # Full conversation history
            with st.expander("📜 Full Conversation History"):
                conversation = review.get("conversation_history", [])
                for message in conversation:
                    speaker = message.get("speaker", "Unknown")
                    content = message.get("content", "")
                    st.write(f"**{speaker}:** {content}")
                    st.write("---")
        
        else:
            st.info("👆 Complete a code review to see team discussion.")
    
    with tab4:
        st.header("📋 Review History")
        
        history = st.session_state.review_team.get_review_history()
        
        if history:
            # Display history table
            st.subheader("📊 Previous Reviews")
            
            for i, review_summary in enumerate(reversed(history)):
                with st.expander(f"Review {len(history)-i}: {review_summary.get('language', 'Unknown')} - Score: {review_summary.get('overall_score', 0)}/10"):
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.write(f"**Language:** {review_summary.get('language', 'Unknown')}")
                        st.write(f"**Timestamp:** {review_summary.get('timestamp', 'Unknown')}")
                    
                    with col2:
                        st.write(f"**Issues Found:** {review_summary.get('issues_found', 0)}")
                        st.write(f"**Overall Score:** {review_summary.get('overall_score', 0)}/10")
                    
                    with col3:
                        if st.button(f"Load Review {len(history)-i}", key=f"load_{i}"):
                            # Find and load the full review
                            review_id = review_summary.get('review_id')
                            for full_review in st.session_state.review_history:
                                if full_review.get('review_id') == review_id:
                                    st.session_state.current_review = full_review
                                    st.rerun()
                                    break
        else:
            st.info("No reviews conducted yet. Submit code for review to build history.")

# Sample code functions
def get_python_sample():
    return '''def process_user_data(user_input):
    # Process user input without validation
    query = "SELECT * FROM users WHERE id = " + user_input
    result = execute_query(query)
    return result

def calculate_total(items):
    total = 0
    for item in items:
        total = total + item.price * item.quantity
    return total'''

def get_javascript_sample():
    return '''function validateUser(username, password) {
    if (username && password) {
        // Simple validation
        if (password.length > 6) {
            return true;
        }
    }
    return false;
}

function processPayment(amount, cardNumber) {
    // Process payment without encryption
    console.log("Processing payment for: " + amount);
    console.log("Card number: " + cardNumber);
    return submitPayment(amount, cardNumber);
}'''

def get_security_sample():
    return '''import os
import subprocess

def execute_command(user_command):
    # Execute user command directly - SECURITY RISK!
    result = subprocess.run(user_command, shell=True, capture_output=True)
    return result.stdout

def get_user_file(filename):
    # Read file without path validation - SECURITY RISK!
    file_path = "/uploads/" + filename
    with open(file_path, 'r') as f:
        return f.read()'''

if __name__ == "__main__":
    main()
