{"ast": null, "code": "import { constructFrom } from \"./constructFrom.js\";\nimport { isSaturday } from \"./isSaturday.js\";\nimport { isSunday } from \"./isSunday.js\";\nimport { isWeekend } from \"./isWeekend.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addBusinessDays} function options.\n */\n\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of business days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the business days added\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\nexport function addBusinessDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  const startedOnWeekend = isWeekend(_date, options);\n  if (isNaN(amount)) return constructFrom(options?.in, NaN);\n  const hours = _date.getHours();\n  const sign = amount < 0 ? -1 : 1;\n  const fullWeeks = Math.trunc(amount / 5);\n  _date.setDate(_date.getDate() + fullWeeks * 7);\n\n  // Get remaining days not part of a full week\n  let restDays = Math.abs(amount % 5);\n\n  // Loops over remaining days\n  while (restDays > 0) {\n    _date.setDate(_date.getDate() + sign);\n    if (!isWeekend(_date, options)) restDays -= 1;\n  }\n\n  // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n  if (startedOnWeekend && isWeekend(_date, options) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(_date, options)) _date.setDate(_date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(_date, options)) _date.setDate(_date.getDate() + (sign < 0 ? 1 : -2));\n  }\n\n  // Restore hours to avoid DST lag\n  _date.setHours(hours);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addBusinessDays;", "map": {"version": 3, "names": ["constructFrom", "isSaturday", "is<PERSON><PERSON><PERSON>", "isWeekend", "toDate", "addBusinessDays", "date", "amount", "options", "_date", "in", "startedOnWeekend", "isNaN", "NaN", "hours", "getHours", "sign", "fullWeeks", "Math", "trunc", "setDate", "getDate", "restDays", "abs", "setHours"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI/frontend/node_modules/date-fns/addBusinessDays.js"], "sourcesContent": ["import { constructFrom } from \"./constructFrom.js\";\nimport { isSaturday } from \"./isSaturday.js\";\nimport { isSunday } from \"./isSunday.js\";\nimport { isWeekend } from \"./isWeekend.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addBusinessDays} function options.\n */\n\n/**\n * @name addBusinessDays\n * @category Day Helpers\n * @summary Add the specified number of business days (mon - fri) to the given date.\n *\n * @description\n * Add the specified number of business days (mon - fri) to the given date, ignoring weekends.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of business days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the business days added\n *\n * @example\n * // Add 10 business days to 1 September 2014:\n * const result = addBusinessDays(new Date(2014, 8, 1), 10)\n * //=> Mon Sep 15 2014 00:00:00 (skipped weekend days)\n */\nexport function addBusinessDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  const startedOnWeekend = isWeekend(_date, options);\n\n  if (isNaN(amount)) return constructFrom(options?.in, NaN);\n\n  const hours = _date.getHours();\n  const sign = amount < 0 ? -1 : 1;\n  const fullWeeks = Math.trunc(amount / 5);\n\n  _date.setDate(_date.getDate() + fullWeeks * 7);\n\n  // Get remaining days not part of a full week\n  let restDays = Math.abs(amount % 5);\n\n  // Loops over remaining days\n  while (restDays > 0) {\n    _date.setDate(_date.getDate() + sign);\n    if (!isWeekend(_date, options)) restDays -= 1;\n  }\n\n  // If the date is a weekend day and we reduce a dividable of\n  // 5 from it, we land on a weekend date.\n  // To counter this, we add days accordingly to land on the next business day\n  if (startedOnWeekend && isWeekend(_date, options) && amount !== 0) {\n    // If we're reducing days, we want to add days until we land on a weekday\n    // If we're adding days we want to reduce days until we land on a weekday\n    if (isSaturday(_date, options))\n      _date.setDate(_date.getDate() + (sign < 0 ? 2 : -1));\n    if (isSunday(_date, options))\n      _date.setDate(_date.getDate() + (sign < 0 ? 1 : -2));\n  }\n\n  // Restore hours to avoid DST lag\n  _date.setHours(hours);\n\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addBusinessDays;\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,oBAAoB;AAClD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,MAAM,QAAQ,aAAa;;AAEpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,MAAMC,KAAK,GAAGL,MAAM,CAACE,IAAI,EAAEE,OAAO,EAAEE,EAAE,CAAC;EACvC,MAAMC,gBAAgB,GAAGR,SAAS,CAACM,KAAK,EAAED,OAAO,CAAC;EAElD,IAAII,KAAK,CAACL,MAAM,CAAC,EAAE,OAAOP,aAAa,CAACQ,OAAO,EAAEE,EAAE,EAAEG,GAAG,CAAC;EAEzD,MAAMC,KAAK,GAAGL,KAAK,CAACM,QAAQ,CAAC,CAAC;EAC9B,MAAMC,IAAI,GAAGT,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAChC,MAAMU,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACZ,MAAM,GAAG,CAAC,CAAC;EAExCE,KAAK,CAACW,OAAO,CAACX,KAAK,CAACY,OAAO,CAAC,CAAC,GAAGJ,SAAS,GAAG,CAAC,CAAC;;EAE9C;EACA,IAAIK,QAAQ,GAAGJ,IAAI,CAACK,GAAG,CAAChB,MAAM,GAAG,CAAC,CAAC;;EAEnC;EACA,OAAOe,QAAQ,GAAG,CAAC,EAAE;IACnBb,KAAK,CAACW,OAAO,CAACX,KAAK,CAACY,OAAO,CAAC,CAAC,GAAGL,IAAI,CAAC;IACrC,IAAI,CAACb,SAAS,CAACM,KAAK,EAAED,OAAO,CAAC,EAAEc,QAAQ,IAAI,CAAC;EAC/C;;EAEA;EACA;EACA;EACA,IAAIX,gBAAgB,IAAIR,SAAS,CAACM,KAAK,EAAED,OAAO,CAAC,IAAID,MAAM,KAAK,CAAC,EAAE;IACjE;IACA;IACA,IAAIN,UAAU,CAACQ,KAAK,EAAED,OAAO,CAAC,EAC5BC,KAAK,CAACW,OAAO,CAACX,KAAK,CAACY,OAAO,CAAC,CAAC,IAAIL,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACtD,IAAId,QAAQ,CAACO,KAAK,EAAED,OAAO,CAAC,EAC1BC,KAAK,CAACW,OAAO,CAACX,KAAK,CAACY,OAAO,CAAC,CAAC,IAAIL,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EACxD;;EAEA;EACAP,KAAK,CAACe,QAAQ,CAACV,KAAK,CAAC;EAErB,OAAOL,KAAK;AACd;;AAEA;AACA,eAAeJ,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}