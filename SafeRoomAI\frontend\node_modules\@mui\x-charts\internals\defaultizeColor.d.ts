import { AllSeriesType } from '../models/seriesType';
export declare function defaultizeColor(series: AllSeriesType, seriesIndex: number, colors?: string[]): {
    data: {
        label?: string | ((location: "tooltip" | "legend" | "arc") => string) | undefined;
        color: string;
        value: number;
        id?: import("..").PieItemId | undefined;
    }[];
    type: "pie";
    innerRadius?: number | string;
    outerRadius?: number | string;
    arcLabelRadius?: number | string;
    cornerRadius?: number;
    startAngle?: number;
    endAngle?: number;
    paddingAngle?: number;
    sortingValues?: import("..").ChartsPieSorting;
    arcLabel?: "formattedValue" | "label" | "value" | ((item: Omit<import("..").DefaultizedPieValueType, "label"> & {
        label?: string;
    }) => string);
    arcLabelMinAngle?: number;
    cx?: number | string;
    cy?: number | string;
    highlighted?: {
        additionalRadius?: number;
        innerRadius?: number;
        outerRadius?: number;
        cornerRadius?: number;
        paddingAngle?: number;
        arcLabelRadius?: number;
        color?: string;
    };
    faded?: {
        additionalRadius?: number;
        innerRadius?: number;
        outerRadius?: number;
        cornerRadius?: number;
        paddingAngle?: number;
        arcLabelRadius?: number;
        color?: string;
    };
    id?: import(".").SeriesId;
    color?: string;
    valueFormatter?: import(".").SeriesValueFormatter<import(".").MakeOptional<import("..").PieValueType, "id">> | undefined;
    highlightScope?: Partial<import("..").HighlightScope>;
} | {
    type: "bar";
    data?: (number | null)[];
    dataKey?: string;
    label?: string | ((location: "tooltip" | "legend") => string);
    layout?: "horizontal" | "vertical";
    stackOffset?: import("..").StackOffsetType;
    id?: import(".").SeriesId;
    color: string;
    valueFormatter?: import(".").SeriesValueFormatter<number | null> | undefined;
    highlightScope?: Partial<import("..").HighlightScope>;
    xAxisKey?: string;
    yAxisKey?: string;
    xAxisId?: string;
    yAxisId?: string;
    stack?: string;
    stackOrder?: import("..").StackOrderType;
} | {
    type: "scatter";
    data?: import("..").ScatterValueType[];
    markerSize?: number;
    label?: string | ((location: "tooltip" | "legend") => string);
    disableHover?: boolean;
    zAxisKey?: string;
    zAxisId?: string;
    datasetKeys?: {
        x: string;
        y: string;
        z?: string;
        id: string;
    };
    id?: import(".").SeriesId;
    color: string;
    valueFormatter?: import(".").SeriesValueFormatter<import("..").ScatterValueType> | undefined;
    highlightScope?: Partial<import("..").HighlightScope>;
    xAxisKey?: string;
    yAxisKey?: string;
    xAxisId?: string;
    yAxisId?: string;
} | {
    type: "line";
    data?: (number | null)[];
    dataKey?: string;
    stack?: string;
    area?: boolean;
    label?: string | ((location: "tooltip" | "legend") => string);
    curve?: import("..").CurveType;
    showMark?: boolean | ((params: import("..").ShowMarkParams) => boolean);
    disableHighlight?: boolean;
    connectNulls?: boolean;
    stackOffset?: import("..").StackOffsetType;
    baseline?: number | "min" | "max";
    id?: import(".").SeriesId;
    color: string;
    valueFormatter?: import(".").SeriesValueFormatter<number | null> | undefined;
    highlightScope?: Partial<import("..").HighlightScope>;
    xAxisKey?: string;
    yAxisKey?: string;
    xAxisId?: string;
    yAxisId?: string;
    stackOrder?: import("..").StackOrderType;
};
