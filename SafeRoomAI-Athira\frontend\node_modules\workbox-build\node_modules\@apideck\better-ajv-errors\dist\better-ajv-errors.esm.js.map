{"version": 3, "file": "better-ajv-errors.esm.js", "sources": ["../src/constants.ts", "../src/lib/filter.ts", "../src/lib/suggestions.ts", "../src/lib/utils.ts", "../src/index.ts"], "sourcesContent": ["import { DefinedError } from 'ajv';\n\nexport const AJV_ERROR_KEYWORD_WEIGHT_MAP: Partial<Record<DefinedError['keyword'], number>> = {\n  enum: 1,\n  type: 0,\n};\n\nexport const QUOTES_REGEX = /\"/g;\nexport const NOT_REGEX = /NOT/g;\nexport const SLASH_REGEX = /\\//g;\n", "import { DefinedError } from 'ajv';\nimport { AJV_ERROR_KEYWORD_WEIGHT_MAP } from '../constants';\n\nexport const filterSingleErrorPerProperty = (errors: DefinedError[]): DefinedError[] => {\n  const errorsPerProperty = errors.reduce<Record<string, DefinedError>>((acc, error) => {\n    const prop =\n      error.instancePath + ((error.params as any)?.additionalProperty ?? (error.params as any)?.missingProperty ?? '');\n    const existingError = acc[prop];\n    if (!existingError) {\n      acc[prop] = error;\n      return acc;\n    }\n    const weight = AJV_ERROR_KEYWORD_WEIGHT_MAP[error.keyword] ?? 0;\n    const existingWeight = AJV_ERROR_KEYWORD_WEIGHT_MAP[existingError.keyword] ?? 0;\n\n    if (weight > existingWeight) {\n      acc[prop] = error;\n    }\n    return acc;\n  }, {});\n\n  return Object.values(errorsPerProperty);\n};\n", "import leven from 'leven';\n\nexport const getSuggestion = ({\n  value,\n  suggestions,\n  format = (suggestion) => `Did you mean '${suggestion}'?`,\n}: {\n  value: string | null;\n  suggestions: string[];\n  format?: (suggestion: string) => string;\n}): string => {\n  if (!value) return '';\n  const bestSuggestion = suggestions.reduce(\n    (best, current) => {\n      const distance = leven(value, current);\n      if (best.distance > distance) {\n        return { value: current, distance };\n      }\n\n      return best;\n    },\n    {\n      distance: Infinity,\n      value: '',\n    }\n  );\n\n  return bestSuggestion.distance < value.length ? format(bestSuggestion.value) : '';\n};\n", "import { NOT_REGEX, QUOTES_REGEX, SLASH_REGEX } from '../constants';\nimport pointer from 'jsonpointer';\n\nexport const pointerToDotNotation = (pointer: string): string => {\n  return pointer.replace(SLASH_REGEX, '.');\n};\n\nexport const cleanAjvMessage = (message: string): string => {\n  return message.replace(QUOTES_REGEX, \"'\").replace(NOT_REGEX, 'not');\n};\n\nexport const getLastSegment = (path: string): string => {\n  const segments = path.split('/');\n  return segments.pop() as string;\n};\n\nexport const safeJsonPointer = <T>({ object, pnter, fallback }: { object: any; pnter: string; fallback: T }): T => {\n  try {\n    return pointer.get(object, pnter);\n  } catch (err) {\n    return fallback;\n  }\n};\n", "import { DefinedError, ErrorObject } from 'ajv';\nimport type { JSONSchema6 } from 'json-schema';\nimport { ValidationError } from './types/ValidationError';\nimport { filterSingleErrorPerProperty } from './lib/filter';\nimport { getSuggestion } from './lib/suggestions';\nimport { cleanAjvMessage, getLastSegment, pointerToDotNotation, safeJsonPointer } from './lib/utils';\n\nexport interface BetterAjvErrorsOptions {\n  errors: ErrorObject[] | null | undefined;\n  data: any;\n  schema: JSONSchema6;\n  basePath?: string;\n}\n\nexport const betterAjvErrors = ({\n  errors,\n  data,\n  schema,\n  basePath = '{base}',\n}: BetterAjvErrorsOptions): ValidationError[] => {\n  if (!Array.isArray(errors) || errors.length === 0) {\n    return [];\n  }\n\n  const definedErrors = filterSingleErrorPerProperty(errors as DefinedError[]);\n\n  return definedErrors.map((error) => {\n    const path = pointerToDotNotation(basePath + error.instancePath);\n    const prop = getLastSegment(error.instancePath);\n    const defaultContext = {\n      errorType: error.keyword,\n    };\n    const defaultMessage = `${prop ? `property '${prop}'` : path} ${cleanAjvMessage(error.message as string)}`;\n\n    let validationError: ValidationError;\n\n    switch (error.keyword) {\n      case 'additionalProperties': {\n        const additionalProp = error.params.additionalProperty;\n        const suggestionPointer = error.schemaPath.replace('#', '').replace('/additionalProperties', '');\n        const { properties } = safeJsonPointer({\n          object: schema,\n          pnter: suggestionPointer,\n          fallback: { properties: {} },\n        });\n        validationError = {\n          message: `'${additionalProp}' property is not expected to be here`,\n          suggestion: getSuggestion({\n            value: additionalProp,\n            suggestions: Object.keys(properties ?? {}),\n            format: (suggestion) => `Did you mean property '${suggestion}'?`,\n          }),\n          path,\n          context: defaultContext,\n        };\n        break;\n      }\n      case 'enum': {\n        const suggestions = error.params.allowedValues.map((value) => value.toString());\n        const prop = getLastSegment(error.instancePath);\n        const value = safeJsonPointer({ object: data, pnter: error.instancePath, fallback: '' });\n        validationError = {\n          message: `'${prop}' property must be equal to one of the allowed values`,\n          suggestion: getSuggestion({\n            value,\n            suggestions,\n          }),\n          path,\n          context: {\n            ...defaultContext,\n            allowedValues: error.params.allowedValues,\n          },\n        };\n        break;\n      }\n      case 'type': {\n        const prop = getLastSegment(error.instancePath);\n        const type = error.params.type;\n        validationError = {\n          message: `'${prop}' property type must be ${type}`,\n          path,\n          context: defaultContext,\n        };\n        break;\n      }\n      case 'required': {\n        validationError = {\n          message: `${path} must have required property '${error.params.missingProperty}'`,\n          path,\n          context: defaultContext,\n        };\n        break;\n      }\n      case 'const': {\n        return {\n          message: `'${prop}' property must be equal to the allowed value`,\n          path,\n          context: {\n            ...defaultContext,\n            allowedValue: error.params.allowedValue,\n          },\n        };\n      }\n\n      default:\n        return { message: defaultMessage, path, context: defaultContext };\n    }\n\n    // Remove empty properties\n    const errorEntries = Object.entries(validationError);\n    for (const [key, value] of errorEntries as [keyof ValidationError, unknown][]) {\n      if (value === null || value === undefined || value === '') {\n        delete validationError[key];\n      }\n    }\n\n    return validationError;\n  });\n};\n\nexport { ValidationError };\n"], "names": ["AJV_ERROR_KEYWORD_WEIGHT_MAP", "type", "QUOTES_REGEX", "NOT_REGEX", "SLASH_REGEX", "filterSingleErrorPerProperty", "errors", "errorsPerProperty", "reduce", "acc", "error", "prop", "instancePath", "params", "additionalProperty", "missingProperty", "existingError", "weight", "keyword", "existingWeight", "Object", "values", "getSuggestion", "value", "suggestions", "format", "suggestion", "bestSuggestion", "best", "current", "distance", "leven", "Infinity", "length", "pointerToDotNotation", "pointer", "replace", "cleanAjvMessage", "message", "getLastSegment", "path", "segments", "split", "pop", "safe<PERSON>sonPointer", "object", "pnter", "fallback", "get", "err", "betterAjvErrors", "data", "schema", "basePath", "Array", "isArray", "definedErrors", "map", "defaultContext", "errorType", "defaultMessage", "validationError", "additionalProp", "suggestionPointer", "schemaPath", "properties", "keys", "context", "<PERSON><PERSON><PERSON><PERSON>", "toString", "allowedValue", "errorEntries", "entries", "key", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAEO,IAAMA,4BAA4B,GAAqD;EAC5F,QAAM,CADsF;EAE5FC,IAAI,EAAE;AAFsF,CAAvF;AAKA,IAAMC,YAAY,GAAG,IAArB;AACA,IAAMC,SAAS,GAAG,MAAlB;AACA,IAAMC,WAAW,GAAG,KAApB;;ACNA,IAAMC,4BAA4B,GAAG,SAA/BA,4BAA+B,CAACC,MAAD;EAC1C,IAAMC,iBAAiB,GAAGD,MAAM,CAACE,MAAP,CAA4C,UAACC,GAAD,EAAMC,KAAN;;;IACpE,IAAMC,IAAI,GACRD,KAAK,CAACE,YAAN,sDAAuBF,KAAK,CAACG,MAA7B,qBAAuB,cAAsBC,kBAA7C,sDAAoEJ,KAAK,CAACG,MAA1E,qBAAoE,eAAsBE,eAA1F,mBAA6G,EAA7G,CADF;IAEA,IAAMC,aAAa,GAAGP,GAAG,CAACE,IAAD,CAAzB;;IACA,IAAI,CAACK,aAAL,EAAoB;MAClBP,GAAG,CAACE,IAAD,CAAH,GAAYD,KAAZ;MACA,OAAOD,GAAP;;;IAEF,IAAMQ,MAAM,4BAAGjB,4BAA4B,CAACU,KAAK,CAACQ,OAAP,CAA/B,oCAAkD,CAA9D;IACA,IAAMC,cAAc,6BAAGnB,4BAA4B,CAACgB,aAAa,CAACE,OAAf,CAA/B,qCAA0D,CAA9E;;IAEA,IAAID,MAAM,GAAGE,cAAb,EAA6B;MAC3BV,GAAG,CAACE,IAAD,CAAH,GAAYD,KAAZ;;;IAEF,OAAOD,GAAP;GAdwB,EAevB,EAfuB,CAA1B;EAiBA,OAAOW,MAAM,CAACC,MAAP,CAAcd,iBAAd,CAAP;AACD,CAnBM;;ACDA,IAAMe,aAAa,GAAG,SAAhBA,aAAgB;MAC3BC,aAAAA;MACAC,mBAAAA;yBACAC;MAAAA,kCAAS,UAACC,UAAD;IAAA,0BAAiCA,UAAjC;;EAMT,IAAI,CAACH,KAAL,EAAY,OAAO,EAAP;EACZ,IAAMI,cAAc,GAAGH,WAAW,CAAChB,MAAZ,CACrB,UAACoB,IAAD,EAAOC,OAAP;IACE,IAAMC,QAAQ,GAAGC,KAAK,CAACR,KAAD,EAAQM,OAAR,CAAtB;;IACA,IAAID,IAAI,CAACE,QAAL,GAAgBA,QAApB,EAA8B;MAC5B,OAAO;QAAEP,KAAK,EAAEM,OAAT;QAAkBC,QAAQ,EAARA;OAAzB;;;IAGF,OAAOF,IAAP;GAPmB,EASrB;IACEE,QAAQ,EAAEE,QADZ;IAEET,KAAK,EAAE;GAXY,CAAvB;EAeA,OAAOI,cAAc,CAACG,QAAf,GAA0BP,KAAK,CAACU,MAAhC,GAAyCR,MAAM,CAACE,cAAc,CAACJ,KAAhB,CAA/C,GAAwE,EAA/E;AACD,CA1BM;;ACCA,IAAMW,oBAAoB,GAAG,SAAvBA,oBAAuB,CAACC,OAAD;EAClC,OAAOA,OAAO,CAACC,OAAR,CAAgBhC,WAAhB,EAA6B,GAA7B,CAAP;AACD,CAFM;AAIP,AAAO,IAAMiC,eAAe,GAAG,SAAlBA,eAAkB,CAACC,OAAD;EAC7B,OAAOA,OAAO,CAACF,OAAR,CAAgBlC,YAAhB,EAA8B,GAA9B,EAAmCkC,OAAnC,CAA2CjC,SAA3C,EAAsD,KAAtD,CAAP;AACD,CAFM;AAIP,AAAO,IAAMoC,cAAc,GAAG,SAAjBA,cAAiB,CAACC,IAAD;EAC5B,IAAMC,QAAQ,GAAGD,IAAI,CAACE,KAAL,CAAW,GAAX,CAAjB;EACA,OAAOD,QAAQ,CAACE,GAAT,EAAP;AACD,CAHM;AAKP,AAAO,IAAMC,eAAe,GAAG,SAAlBA,eAAkB;MAAMC,cAAAA;MAAQC,aAAAA;MAAOC,gBAAAA;;EAClD,IAAI;IACF,OAAOZ,OAAO,CAACa,GAAR,CAAYH,MAAZ,EAAoBC,KAApB,CAAP;GADF,CAEE,OAAOG,GAAP,EAAY;IACZ,OAAOF,QAAP;;AAEH,CANM;;ICFMG,eAAe,GAAG,SAAlBA,eAAkB;MAC7B5C,cAAAA;MACA6C,YAAAA;MACAC,cAAAA;2BACAC;MAAAA,sCAAW;;EAEX,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcjD,MAAd,CAAD,IAA0BA,MAAM,CAAC2B,MAAP,KAAkB,CAAhD,EAAmD;IACjD,OAAO,EAAP;;;EAGF,IAAMuB,aAAa,GAAGnD,4BAA4B,CAACC,MAAD,CAAlD;EAEA,OAAOkD,aAAa,CAACC,GAAd,CAAkB,UAAC/C,KAAD;IACvB,IAAM8B,IAAI,GAAGN,oBAAoB,CAACmB,QAAQ,GAAG3C,KAAK,CAACE,YAAlB,CAAjC;IACA,IAAMD,IAAI,GAAG4B,cAAc,CAAC7B,KAAK,CAACE,YAAP,CAA3B;IACA,IAAM8C,cAAc,GAAG;MACrBC,SAAS,EAAEjD,KAAK,CAACQ;KADnB;IAGA,IAAM0C,cAAc,IAAMjD,IAAI,kBAAgBA,IAAhB,SAA0B6B,IAApC,UAA4CH,eAAe,CAAC3B,KAAK,CAAC4B,OAAP,CAA/E;IAEA,IAAIuB,eAAJ;;IAEA,QAAQnD,KAAK,CAACQ,OAAd;MACE,KAAK,sBAAL;QAA6B;UAC3B,IAAM4C,cAAc,GAAGpD,KAAK,CAACG,MAAN,CAAaC,kBAApC;UACA,IAAMiD,iBAAiB,GAAGrD,KAAK,CAACsD,UAAN,CAAiB5B,OAAjB,CAAyB,GAAzB,EAA8B,EAA9B,EAAkCA,OAAlC,CAA0C,uBAA1C,EAAmE,EAAnE,CAA1B;;UACA,uBAAuBQ,eAAe,CAAC;YACrCC,MAAM,EAAEO,MAD6B;YAErCN,KAAK,EAAEiB,iBAF8B;YAGrChB,QAAQ,EAAE;cAAEkB,UAAU,EAAE;;WAHY,CAAtC;cAAQA,UAAR,oBAAQA,UAAR;;UAKAJ,eAAe,GAAG;YAChBvB,OAAO,QAAMwB,cAAN,0CADS;YAEhBpC,UAAU,EAAEJ,aAAa,CAAC;cACxBC,KAAK,EAAEuC,cADiB;cAExBtC,WAAW,EAAEJ,MAAM,CAAC8C,IAAP,CAAYD,UAAZ,WAAYA,UAAZ,GAA0B,EAA1B,CAFW;cAGxBxC,MAAM,EAAE,gBAACC,UAAD;gBAAA,mCAA0CA,UAA1C;;aAHe,CAFT;YAOhBc,IAAI,EAAJA,IAPgB;YAQhB2B,OAAO,EAAET;WARX;UAUA;;;MAEF,KAAK,MAAL;QAAa;UACX,IAAMlC,WAAW,GAAGd,KAAK,CAACG,MAAN,CAAauD,aAAb,CAA2BX,GAA3B,CAA+B,UAAClC,KAAD;YAAA,OAAWA,KAAK,CAAC8C,QAAN,EAAX;WAA/B,CAApB;;UACA,IAAM1D,KAAI,GAAG4B,cAAc,CAAC7B,KAAK,CAACE,YAAP,CAA3B;;UACA,IAAMW,KAAK,GAAGqB,eAAe,CAAC;YAAEC,MAAM,EAAEM,IAAV;YAAgBL,KAAK,EAAEpC,KAAK,CAACE,YAA7B;YAA2CmC,QAAQ,EAAE;WAAtD,CAA7B;UACAc,eAAe,GAAG;YAChBvB,OAAO,QAAM3B,KAAN,0DADS;YAEhBe,UAAU,EAAEJ,aAAa,CAAC;cACxBC,KAAK,EAALA,KADwB;cAExBC,WAAW,EAAXA;aAFuB,CAFT;YAMhBgB,IAAI,EAAJA,IANgB;YAOhB2B,OAAO,eACFT,cADE;cAELU,aAAa,EAAE1D,KAAK,CAACG,MAAN,CAAauD;;WAThC;UAYA;;;MAEF,KAAK,MAAL;QAAa;UACX,IAAMzD,MAAI,GAAG4B,cAAc,CAAC7B,KAAK,CAACE,YAAP,CAA3B;;UACA,IAAMX,IAAI,GAAGS,KAAK,CAACG,MAAN,CAAaZ,IAA1B;UACA4D,eAAe,GAAG;YAChBvB,OAAO,QAAM3B,MAAN,gCAAqCV,IAD5B;YAEhBuC,IAAI,EAAJA,IAFgB;YAGhB2B,OAAO,EAAET;WAHX;UAKA;;;MAEF,KAAK,UAAL;QAAiB;UACfG,eAAe,GAAG;YAChBvB,OAAO,EAAKE,IAAL,sCAA0C9B,KAAK,CAACG,MAAN,CAAaE,eAAvD,MADS;YAEhByB,IAAI,EAAJA,IAFgB;YAGhB2B,OAAO,EAAET;WAHX;UAKA;;;MAEF,KAAK,OAAL;QAAc;UACZ,OAAO;YACLpB,OAAO,QAAM3B,IAAN,kDADF;YAEL6B,IAAI,EAAJA,IAFK;YAGL2B,OAAO,eACFT,cADE;cAELY,YAAY,EAAE5D,KAAK,CAACG,MAAN,CAAayD;;WAL/B;;;MAUF;QACE,OAAO;UAAEhC,OAAO,EAAEsB,cAAX;UAA2BpB,IAAI,EAAJA,IAA3B;UAAiC2B,OAAO,EAAET;SAAjD;;;;IAIJ,IAAMa,YAAY,GAAGnD,MAAM,CAACoD,OAAP,CAAeX,eAAf,CAArB;;IACA,iCAA2BU,YAA3B,mCAA+E;MAA1E;UAAOE,GAAP;UAAYlD,MAAZ;;MACH,IAAIA,MAAK,KAAK,IAAV,IAAkBA,MAAK,KAAKmD,SAA5B,IAAyCnD,MAAK,KAAK,EAAvD,EAA2D;QACzD,OAAOsC,eAAe,CAACY,GAAD,CAAtB;;;;IAIJ,OAAOZ,eAAP;GA1FK,CAAP;AA4FD,CAxGM;;;;"}