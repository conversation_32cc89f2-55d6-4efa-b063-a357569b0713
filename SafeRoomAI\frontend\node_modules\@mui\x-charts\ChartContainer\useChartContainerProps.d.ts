import * as React from 'react';
import type { DrawingProviderProps } from '../context/DrawingProvider';
import type { CartesianProviderProps } from '../context/CartesianProvider';
import type { SeriesProviderProps } from '../context/SeriesProvider';
import type { ZAxisContextProviderProps } from '../context/ZAxisContextProvider';
import type { ChartContainerProps } from './ChartContainer';
import { HighlightedProviderProps } from '../context';
import { ChartsSurfaceProps } from '../ChartsSurface';
import { PluginProviderProps } from '../context/PluginProvider';
import { AnimationProviderProps } from '../context/AnimationProvider';
export declare const useChartContainerProps: (props: ChartContainerProps, ref: React.ForwardedRef<unknown>) => {
    children: React.ReactNode;
    drawingProviderProps: Omit<DrawingProviderProps, "children">;
    seriesProviderProps: Omit<SeriesProviderProps, "children">;
    cartesianProviderProps: Omit<CartesianProviderProps, "children">;
    zAxisContextProps: Omit<ZAxisContextProviderProps, "children">;
    highlightedProviderProps: Omit<HighlightedProviderProps, "children">;
    chartsSurfaceProps: ChartsSurfaceProps & {
        ref: any;
    };
    pluginProviderProps: Omit<PluginProviderProps, "children">;
    animationProviderProps: Omit<AnimationProviderProps, "children">;
    xAxis: {
        reverse?: boolean | undefined;
        valueFormatter?: ((value: any, context: import("../internals").AxisValueFormatterContext) => string) | undefined;
        data?: any[] | undefined;
        dataKey?: string | undefined;
        label?: string | undefined;
        min?: (number | Date) | undefined;
        max?: (number | Date) | undefined;
        tickMaxStep?: number | undefined;
        tickMinStep?: number | undefined;
        tickNumber?: number | undefined;
        disableLine?: boolean | undefined;
        disableTicks?: boolean | undefined;
        fill?: string | undefined;
        tickFontSize?: number | undefined;
        tickLabelStyle?: import("..").ChartsTextStyle | undefined;
        labelStyle?: import("..").ChartsTextStyle | undefined;
        tickLabelInterval?: "auto" | ((value: any, index: number) => boolean) | undefined;
        labelFontSize?: number | undefined;
        stroke?: string | undefined;
        tickSize?: number | undefined;
        classes?: Partial<import("..").ChartsAxisClasses> | undefined;
        slots?: Partial<import("../internals").ChartsAxisSlots> | undefined;
        slotProps?: Partial<import("../internals").ChartsAxisSlotProps> | undefined;
        sx?: import("@mui/system").SxProps | undefined;
        tickInterval?: "auto" | ((value: any, index: number) => boolean) | any[] | undefined;
        tickPlacement?: "start" | "end" | "middle" | "extremities" | undefined;
        tickLabelPlacement?: "middle" | "tick" | undefined;
        scaleType?: "linear" | "band" | "point" | "log" | "pow" | "sqrt" | "time" | "utc" | undefined;
        colorMap?: import("../models/colorMapping").ContinuousColorConfig<number | Date> | import("../models/colorMapping").PiecewiseColorConfig<number | Date> | import("../models/colorMapping").OrdinalColorConfig<string | number | Date> | undefined;
        hideTooltip?: boolean | undefined;
        domainLimit?: "nice" | "strict" | ((min: number, max: number) => {
            min: number;
            max: number;
        }) | undefined;
        id: import("../internals").AxisId;
    }[];
    yAxis: {
        reverse?: boolean | undefined;
        valueFormatter?: ((value: any, context: import("../internals").AxisValueFormatterContext) => string) | undefined;
        data?: any[] | undefined;
        dataKey?: string | undefined;
        label?: string | undefined;
        min?: (number | Date) | undefined;
        max?: (number | Date) | undefined;
        tickMaxStep?: number | undefined;
        tickMinStep?: number | undefined;
        tickNumber?: number | undefined;
        disableLine?: boolean | undefined;
        disableTicks?: boolean | undefined;
        fill?: string | undefined;
        tickFontSize?: number | undefined;
        tickLabelStyle?: import("..").ChartsTextStyle | undefined;
        labelStyle?: import("..").ChartsTextStyle | undefined;
        tickLabelInterval?: "auto" | ((value: any, index: number) => boolean) | undefined;
        labelFontSize?: number | undefined;
        stroke?: string | undefined;
        tickSize?: number | undefined;
        classes?: Partial<import("..").ChartsAxisClasses> | undefined;
        slots?: Partial<import("../internals").ChartsAxisSlots> | undefined;
        slotProps?: Partial<import("../internals").ChartsAxisSlotProps> | undefined;
        sx?: import("@mui/system").SxProps | undefined;
        tickInterval?: "auto" | ((value: any, index: number) => boolean) | any[] | undefined;
        tickPlacement?: "start" | "end" | "middle" | "extremities" | undefined;
        tickLabelPlacement?: "middle" | "tick" | undefined;
        scaleType?: "linear" | "band" | "point" | "log" | "pow" | "sqrt" | "time" | "utc" | undefined;
        colorMap?: import("../models/colorMapping").ContinuousColorConfig<number | Date> | import("../models/colorMapping").PiecewiseColorConfig<number | Date> | import("../models/colorMapping").OrdinalColorConfig<string | number | Date> | undefined;
        hideTooltip?: boolean | undefined;
        domainLimit?: "nice" | "strict" | ((min: number, max: number) => {
            min: number;
            max: number;
        }) | undefined;
        id: import("../internals").AxisId;
    }[];
};
