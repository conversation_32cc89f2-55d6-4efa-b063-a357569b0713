import { formatDistance } from "./nl-BE/_lib/formatDistance.js";
import { formatLong } from "./nl-BE/_lib/formatLong.js";
import { formatRelative } from "./nl-BE/_lib/formatRelative.js";
import { localize } from "./nl-BE/_lib/localize.js";
import { match } from "./nl-BE/_lib/match.js";

/**
 * @category Locales
 * @summary Dutch locale.
 * @language Dutch
 * @iso-639-2 nld
 * <AUTHOR> [@jtangelder](https://github.com/jtangelder)
 * <AUTHOR> [@rubenstolk](https://github.com/rubenstolk)
 * <AUTHOR> [@bitcrumb](https://github.com/bitcrumb)
 * <AUTHOR> [@dcbn](https://github.com/dcbn)
 */
export const nlBE = {
  code: "nl-BE",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default nlBE;
