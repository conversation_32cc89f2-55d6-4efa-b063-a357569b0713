"use strict";
exports.eu = void 0;
var _index = require("./eu/_lib/formatDistance.cjs");
var _index2 = require("./eu/_lib/formatLong.cjs");
var _index3 = require("./eu/_lib/formatRelative.cjs");
var _index4 = require("./eu/_lib/localize.cjs");
var _index5 = require("./eu/_lib/match.cjs");

/**
 * @category Locales
 * @summary Basque locale.
 * @language Basque
 * @iso-639-2 eus
 * <AUTHOR> [@JacobSoderblom](https://github.com/JacobSoderblom)
 */
const eu = (exports.eu = {
  code: "eu",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
