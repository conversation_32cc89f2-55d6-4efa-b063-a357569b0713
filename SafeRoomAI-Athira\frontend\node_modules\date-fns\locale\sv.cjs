"use strict";
exports.sv = void 0;
var _index = require("./sv/_lib/formatDistance.cjs");
var _index2 = require("./sv/_lib/formatLong.cjs");
var _index3 = require("./sv/_lib/formatRelative.cjs");
var _index4 = require("./sv/_lib/localize.cjs");
var _index5 = require("./sv/_lib/match.cjs");

/**
 * @category Locales
 * @summary Swedish locale.
 * @language Swedish
 * @iso-639-2 swe
 * <AUTHOR> [@ejulen](https://github.com/ejulen)
 * <AUTHOR> [@alexandernanberg](https://github.com/alexandernanberg)
 * <AUTHOR> [@limelights](https://github.com/limelights)
 */
const sv = (exports.sv = {
  code: "sv",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
