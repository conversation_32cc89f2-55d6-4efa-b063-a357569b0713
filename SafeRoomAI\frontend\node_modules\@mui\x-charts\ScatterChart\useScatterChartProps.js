'use client';

import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/esm/objectWithoutPropertiesLoose";
const _excluded = ["xAxis", "yAxis", "zAxis", "series", "tooltip", "axisHighlight", "voronoiMaxRadius", "disableVoronoi", "legend", "width", "height", "margin", "colors", "sx", "grid", "topAxis", "leftAxis", "rightAxis", "bottomAxis", "onItemClick", "children", "slots", "slotProps", "loading", "highlightedItem", "onHighlightChange", "className"];
/**
 * A helper function that extracts ScatterChartProps from the input props
 * and returns an object with props for the children components of ScatterChart.
 *
 * @param props The input props for ScatterChart
 * @returns An object with props for the children components of ScatterChart
 */
export const useScatterChartProps = props => {
  const {
      xAxis,
      yAxis,
      zAxis,
      series,
      tooltip,
      axisHighlight,
      voronoiMaxRadius,
      disableVoronoi,
      legend,
      width,
      height,
      margin,
      colors,
      sx,
      grid,
      topAxis,
      leftAxis,
      rightAxis,
      bottomAxis,
      onItemClick,
      children,
      slots,
      slotProps,
      loading,
      highlightedItem,
      onHighlightChange,
      className
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded);
  const chartContainerProps = _extends({}, other, {
    series: series.map(s => _extends({
      type: 'scatter'
    }, s)),
    width,
    height,
    margin,
    colors,
    xAxis,
    yAxis,
    sx,
    highlightedItem,
    onHighlightChange,
    className
  });
  const zAxisProps = {
    zAxis
  };
  const voronoiHandlerProps = {
    voronoiMaxRadius,
    onItemClick: onItemClick
  };
  const chartsAxisProps = {
    topAxis,
    leftAxis,
    rightAxis,
    bottomAxis,
    slots,
    slotProps
  };
  const gridProps = {
    vertical: grid?.vertical,
    horizontal: grid?.horizontal
  };
  const scatterPlotProps = {
    onItemClick: disableVoronoi ? onItemClick : undefined,
    slots,
    slotProps
  };
  const overlayProps = {
    loading,
    slots,
    slotProps
  };
  const legendProps = _extends({}, legend, {
    slots,
    slotProps
  });
  const axisHighlightProps = _extends({
    y: 'none',
    x: 'none'
  }, axisHighlight);
  const tooltipProps = _extends({
    trigger: 'item'
  }, tooltip, {
    slots,
    slotProps
  });
  return {
    chartContainerProps,
    zAxisProps,
    voronoiHandlerProps,
    chartsAxisProps,
    gridProps,
    scatterPlotProps,
    overlayProps,
    legendProps,
    axisHighlightProps,
    tooltipProps,
    children
  };
};