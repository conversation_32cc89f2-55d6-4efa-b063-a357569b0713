import * as React from 'react';
interface CircularConfig {
    /**
     * The start angle (deg).
     * @default 0
     */
    startAngle?: number;
    /**
     * The end angle (deg).
     * @default 360
     */
    endAngle?: number;
    /**
     * The radius between circle center and the beginning of the arc.
     * Can be a number (in px) or a string with a percentage such as '50%'.
     * The '100%' is the maximal radius that fit into the drawing area.
     * @default '80%'
     */
    innerRadius?: number | string;
    /**
     * The radius between circle center and the end of the arc.
     * Can be a number (in px) or a string with a percentage such as '50%'.
     * The '100%' is the maximal radius that fit into the drawing area.
     * @default '100%'
     */
    outerRadius?: number | string;
    /**
     * The radius applied to arc corners (similar to border radius).
     * Set it to '50%' to get rounded arc.
     * @default 0
     */
    cornerRadius?: number | string;
    /**
     * The x coordinate of the arc center.
     * Can be a number (in px) or a string with a percentage such as '50%'.
     * The '100%' is the width the drawing area.
     */
    cx?: number | string;
    /**
     * The y coordinate of the arc center.
     * Can be a number (in px) or a string with a percentage such as '50%'.
     * The '100%' is the height the drawing area.
     */
    cy?: number | string;
}
interface ProcessedCircularConfig {
    /**
     * The start angle (rad).
     */
    startAngle: number;
    /**
     * The end angle (rad).
     */
    endAngle: number;
    /**
     * The radius between circle center and the beginning of the arc.
     */
    innerRadius: number;
    /**
     * The radius between circle center and the end of the arc.
     */
    outerRadius: number;
    /**
     * The radius applied to arc corners (similar to border radius).
     */
    cornerRadius: number;
    /**
     * The x coordinate of the pie center.
     */
    cx: number;
    /**
     * The y coordinate of the pie center.
     */
    cy: number;
}
interface GaugeConfig {
    /**
     * The value of the gauge.
     * Set to `null` to not display a value.
     */
    value?: number | null;
    /**
     * The minimal value of the gauge.
     * @default 0
     */
    valueMin?: number;
    /**
     * The maximal value of the gauge.
     * @default 100
     */
    valueMax?: number;
}
export declare const GaugeContext: React.Context<Required<GaugeConfig> & ProcessedCircularConfig & {
    /**
     * The maximal radius from (cx, cy) that fits the arc in the drawing area.
     */
    maxRadius: number;
    /**
     * The angle (rad) associated to the current value.
     */
    valueAngle: null | number;
}>;
export interface GaugeProviderProps extends GaugeConfig, CircularConfig {
    children: React.ReactNode;
}
export declare function GaugeProvider(props: GaugeProviderProps): React.JSX.Element;
export declare function useGaugeState(): Required<GaugeConfig> & ProcessedCircularConfig & {
    /**
     * The maximal radius from (cx, cy) that fits the arc in the drawing area.
     */
    maxRadius: number;
    /**
     * The angle (rad) associated to the current value.
     */
    valueAngle: null | number;
};
export {};
