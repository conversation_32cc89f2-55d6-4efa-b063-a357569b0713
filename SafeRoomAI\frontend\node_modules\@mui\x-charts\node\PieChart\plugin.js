"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.plugin = void 0;
var _formatter = _interopRequireDefault(require("./formatter"));
var _getColor = _interopRequireDefault(require("./getColor"));
const plugin = exports.plugin = {
  seriesType: 'pie',
  colorProcessor: _getColor.default,
  seriesFormatter: _formatter.default
};