/// <reference types="react" />
import { TabsProviderValue } from './TabsProvider';
export interface UseTabsParameters {
    /**
     * The value of the currently selected `Tab`.
     * If you don't want any selected `Tab`, you can set this prop to `false`.
     */
    value?: string | number | null;
    /**
     * The default value. Use when the component is not controlled.
     */
    defaultValue?: string | number | null;
    /**
     * The component orientation (layout flow direction).
     * @default 'horizontal'
     */
    orientation?: 'horizontal' | 'vertical';
    /**
     * The direction of the text.
     * @default 'ltr'
     */
    direction?: 'ltr' | 'rtl';
    /**
     * Callback invoked when new value is being set.
     */
    onChange?: (event: React.SyntheticEvent | null, value: number | string | null) => void;
    /**
     * If `true` the selected tab changes on focus. Otherwise it only
     * changes on activation.
     */
    selectionFollowsFocus?: boolean;
}
export interface UseTabsReturnValue {
    /**
     * Returns the values to be passed to the tabs provider.
     */
    contextValue: TabsProviderValue;
}
