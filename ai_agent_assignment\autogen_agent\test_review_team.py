"""
Test script for the AutoGen Code Review Team Agent
"""

import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from code_review_team import CodeReviewTeam

def test_review_team():
    """Test the AutoGen Code Review Team functionality"""
    
    print("👥 Testing AutoGen Code Review Team Agent")
    print("=" * 50)
    
    # Initialize review team
    try:
        review_team = CodeReviewTeam()
        print("✅ Review team initialized successfully")
        
        # Get team info
        team_info = review_team.get_team_info()
        print(f"Team members: {list(team_info['team_members'].keys())}")
        
    except Exception as e:
        print(f"❌ Failed to initialize review team: {e}")
        return
    
    # Test with sample code snippets
    test_cases = [
        {
            "name": "Simple Python Function",
            "code": '''def add_numbers(a, b):
    return a + b

def main():
    result = add_numbers(5, 3)
    print(result)''',
            "language": "python",
            "description": "Simple addition function"
        },
        
        {
            "name": "Security Vulnerable Code",
            "code": '''import subprocess

def execute_command(user_input):
    # Execute user command directly - SECURITY RISK!
    result = subprocess.run(user_input, shell=True, capture_output=True)
    return result.stdout

def get_user_data(user_id):
    # SQL injection vulnerability
    query = "SELECT * FROM users WHERE id = " + user_id
    return execute_query(query)''',
            "language": "python",
            "description": "Code with security vulnerabilities"
        },
        
        {
            "name": "Performance Issues",
            "code": '''def find_duplicates(items):
    duplicates = []
    for i in range(len(items)):
        for j in range(i + 1, len(items)):
            if items[i] == items[j]:
                if items[i] not in duplicates:
                    duplicates.append(items[i])
    return duplicates

def inefficient_sort(data):
    # Bubble sort - O(n²)
    for i in range(len(data)):
        for j in range(len(data) - 1):
            if data[j] > data[j + 1]:
                data[j], data[j + 1] = data[j + 1], data[j]
    return data''',
            "language": "python",
            "description": "Code with performance issues"
        }
    ]
    
    # Run tests for each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        print("-" * 40)
        
        try:
            # Conduct review
            print("🤖 Starting code review...")
            review_result = review_team.review_code(
                code=test_case['code'],
                language=test_case['language'],
                description=test_case['description']
            )
            
            if review_result.get('success'):
                print("✅ Review completed successfully")
                
                # Display key metrics
                metrics = review_result.get('metrics', {})
                print(f"📊 Overall Score: {metrics.get('overall_score', 0)}/10")
                print(f"📊 Total Issues: {metrics.get('total_issues', 0)}")
                print(f"📊 Review Quality: {metrics.get('review_quality', 'Unknown')}")
                print(f"⏱️  Duration: {review_result.get('duration_seconds', 0):.2f}s")
                
                # Display issues summary
                issues = review_result.get('issues', [])
                if issues:
                    print(f"\n🚨 Issues Found:")
                    for issue in issues[:3]:  # Show first 3 issues
                        print(f"  • {issue.get('severity', 'Unknown')} - {issue.get('category', 'General')}")
                        print(f"    {issue.get('description', 'No description')[:100]}...")
                
                # Display recommendations summary
                recommendations = review_result.get('recommendations', [])
                if recommendations:
                    print(f"\n💡 Recommendations:")
                    for rec in recommendations[:2]:  # Show first 2 recommendations
                        print(f"  • {rec.get('category', 'General')} - {rec.get('agent', 'Unknown')}")
                        print(f"    {rec.get('recommendation', 'No recommendation')[:100]}...")
                
                # Save report
                filename = review_team.save_review_report(review_result)
                print(f"📄 Report saved: {filename}")
                
            else:
                print(f"❌ Review failed: {review_result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test case failed: {e}")
    
    # Test team info and history
    print(f"\n📊 Testing team information...")
    try:
        team_info = review_team.get_team_info()
        print(f"✅ Team info retrieved: {len(team_info['team_members'])} members")
        
        history = review_team.get_review_history()
        print(f"✅ Review history retrieved: {len(history)} reviews")
        
    except Exception as e:
        print(f"❌ Failed to get team info: {e}")
    
    print(f"\n🎉 Testing completed!")

def test_sample_files():
    """Test with sample code files"""
    print(f"\n📁 Testing with sample code files...")
    
    sample_files = [
        "./sample_code/security_vulnerable.py",
        "./sample_code/performance_issues.py"
    ]
    
    review_team = CodeReviewTeam()
    
    for file_path in sample_files:
        if os.path.exists(file_path):
            print(f"\n📄 Testing file: {file_path}")
            
            try:
                with open(file_path, 'r') as f:
                    code_content = f.read()
                
                # Review the file
                review_result = review_team.review_code(
                    code=code_content,
                    language="python",
                    description=f"Code from {file_path}"
                )
                
                if review_result.get('success'):
                    metrics = review_result.get('metrics', {})
                    print(f"✅ File reviewed - Score: {metrics.get('overall_score', 0)}/10")
                    print(f"   Issues: {metrics.get('total_issues', 0)}")
                else:
                    print(f"❌ File review failed: {review_result.get('error')}")
                    
            except Exception as e:
                print(f"❌ Error reading file {file_path}: {e}")
        else:
            print(f"⚠️  Sample file not found: {file_path}")

if __name__ == "__main__":
    # Run basic tests
    test_review_team()
    
    # Test with sample files
    test_sample_files()
