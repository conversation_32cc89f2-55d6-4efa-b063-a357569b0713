import * as React from 'react';
export interface UseControlledProps<T = unknown> {
  /**
   * Holds the component value when it's controlled.
   */
  controlled: T | undefined;
  /**
   * The default value when uncontrolled.
   */
  default: T | undefined;
  /**
   * The component name displayed in warnings.
   */
  name: string;
  /**
   * The name of the state variable displayed in warnings.
   */
  state?: string;
}
export default function useControlled<T = unknown>(props: UseControlledProps<T>): [T, React.Dispatch<React.SetStateAction<T | undefined>>];