{"name": "cssnano-preset-default", "version": "5.2.14", "main": "src/index.js", "types": "types/index.d.ts", "description": "Safe defaults for cssnano which require minimal configuration.", "files": ["LICENSE-MIT", "src", "types"], "license": "MIT", "dependencies": {"css-declaration-sorter": "^6.3.1", "postcss-calc": "^8.2.3", "cssnano-utils": "^3.1.0", "postcss-discard-comments": "^5.1.2", "postcss-convert-values": "^5.1.3", "postcss-discard-duplicates": "^5.1.0", "postcss-colormin": "^5.3.1", "postcss-discard-empty": "^5.1.1", "postcss-discard-overridden": "^5.1.0", "postcss-merge-longhand": "^5.1.7", "postcss-merge-rules": "^5.1.4", "postcss-minify-gradients": "^5.1.1", "postcss-minify-font-values": "^5.1.0", "postcss-minify-params": "^5.1.4", "postcss-normalize-charset": "^5.1.0", "postcss-minify-selectors": "^5.2.1", "postcss-normalize-display-values": "^5.1.0", "postcss-normalize-positions": "^5.1.1", "postcss-normalize-repeat-style": "^5.1.1", "postcss-normalize-string": "^5.1.0", "postcss-normalize-timing-functions": "^5.1.0", "postcss-normalize-unicode": "^5.1.1", "postcss-normalize-whitespace": "^5.1.1", "postcss-normalize-url": "^5.1.0", "postcss-ordered-values": "^5.1.3", "postcss-reduce-initial": "^5.1.2", "postcss-reduce-transforms": "^5.1.0", "postcss-svgo": "^5.1.0", "postcss-unique-selectors": "^5.1.1"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "homepage": "https://github.com/cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": "^10 || ^12 || >=14.0"}, "devDependencies": {"postcss": "^8.2.15"}, "peerDependencies": {"postcss": "^8.2.15"}}