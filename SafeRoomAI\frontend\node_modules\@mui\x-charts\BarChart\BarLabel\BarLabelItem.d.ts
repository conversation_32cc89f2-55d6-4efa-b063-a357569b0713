import * as React from 'react';
import { SlotComponentPropsFromProps } from '../../internals/SlotComponentPropsFromProps';
import { BarLabelOwnerState, BarItem, BarLabelContext } from './BarLabel.types';
import { BarLabelProps } from './BarLabel';
export interface BarLabelSlots {
    /**
     * The component that renders the bar label.
     * @default BarLabel
     */
    barLabel?: React.JSXElementConstructor<BarLabelProps>;
}
export interface BarLabelSlotProps {
    barLabel?: SlotComponentPropsFromProps<BarLabelProps, {}, BarLabelOwnerState>;
}
export type BarLabelItemProps = Omit<BarLabelOwnerState, 'isFaded' | 'isHighlighted'> & Pick<BarLabelProps, 'style'> & {
    /**
     * The props used for each component slot.
     * @default {}
     */
    slotProps?: BarLabelSlotProps;
    /**
     * Overridable component slots.
     * @default {}
     */
    slots?: BarLabelSlots;
    /**
     * The height of the bar.
     */
    height: number;
    /**
     * The width of the bar.
     */
    width: number;
    /**
     * The value of the data point.
     */
    value: number | null;
    /**
     * If provided, the function will be used to format the label of the bar.
     * It can be set to 'value' to display the current value.
     * @param {BarItem} item The item to format.
     * @param {BarLabelContext} context data about the bar.
     * @returns {string} The formatted label.
     */
    barLabel?: 'value' | ((item: BarItem, context: BarLabelContext) => string | null | undefined);
};
/**
 * @ignore - internal component.
 */
declare function BarLabelItem(props: BarLabelItemProps): React.JSX.Element | null;
declare namespace BarLabelItem {
    var propTypes: any;
}
export { BarLabelItem };
