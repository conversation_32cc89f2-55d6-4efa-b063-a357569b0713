"""
AutoGen Code Review Team Agent

This agent uses AutoGen to create a multi-agent code review system
where different AI agents with specialized roles collaborate to provide
comprehensive code analysis.
"""

import os
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional

# AutoGen imports
import autogen

# Local imports
from config import AutoGenConfig
from agents import CodeReviewAgents, ConversationManager, get_agent_descriptions

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CodeReviewTeam:
    """
    AutoGen-based Code Review Team Agent
    
    This agent creates a team of specialized AI agents that collaborate
    to provide comprehensive code reviews covering:
    - Architecture and design
    - Security vulnerabilities
    - Performance optimization
    - Documentation quality
    - Best practices
    """
    
    def __init__(self):
        """Initialize the Code Review Team"""
        self.config = AutoGenConfig()
        self.agent_factory = CodeReviewAgents()
        self.agents = self.agent_factory.create_all_agents()
        self.conversation_manager = ConversationManager(self.agents)
        self.review_history = []
        
        logger.info("Code Review Team initialized successfully")
        logger.info(f"Team members: {list(self.agents.keys())}")
    
    def review_code(self, code: str, language: str = "python", 
                   description: str = "") -> Dict[str, Any]:
        """
        Conduct a comprehensive code review using the agent team
        
        Args:
            code (str): Code to review
            language (str): Programming language
            description (str): Optional description of the code
            
        Returns:
            Dict[str, Any]: Comprehensive review results
        """
        try:
            logger.info(f"Starting code review for {language} code")
            
            # Validate input
            if not code.strip():
                raise ValueError("Code cannot be empty")
            
            # Start timestamp
            start_time = datetime.now()
            
            # Initiate the review conversation
            conversation_history = self.conversation_manager.initiate_review(
                code, language
            )
            
            # End timestamp
            end_time = datetime.now()
            review_duration = (end_time - start_time).total_seconds()
            
            # Process and structure the results
            review_result = self._process_review_results(
                conversation_history, code, language, description, 
                start_time, review_duration
            )
            
            # Store in history
            self.review_history.append(review_result)
            
            logger.info(f"Code review completed in {review_duration:.2f} seconds")
            return review_result
            
        except Exception as e:
            logger.error(f"Error during code review: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "code": code,
                "language": language
            }
    
    def _process_review_results(self, conversation_history: List[Dict], 
                              code: str, language: str, description: str,
                              start_time: datetime, duration: float) -> Dict[str, Any]:
        """Process conversation history into structured review results"""
        
        # Extract feedback by agent
        agent_feedback = {}
        team_discussion = []
        final_summary = ""
        
        for message in conversation_history:
            speaker = message.get("speaker", "Unknown")
            content = message.get("content", "")
            
            # Categorize messages
            if speaker in ["Senior_Developer", "Security_Expert", 
                          "Performance_Analyst", "Documentation_Specialist"]:
                if speaker not in agent_feedback:
                    agent_feedback[speaker] = []
                agent_feedback[speaker].append(content)
            
            elif speaker == "Team_Lead":
                if "final" in content.lower() or "summary" in content.lower():
                    final_summary = content
                else:
                    team_discussion.append({"speaker": speaker, "content": content})
            
            else:
                team_discussion.append({"speaker": speaker, "content": content})
        
        # Extract issues and recommendations
        issues = self._extract_issues(agent_feedback)
        recommendations = self._extract_recommendations(agent_feedback)
        
        # Calculate metrics
        metrics = self._calculate_review_metrics(conversation_history, issues)
        
        return {
            "success": True,
            "review_id": f"review_{start_time.strftime('%Y%m%d_%H%M%S')}",
            "timestamp": start_time.isoformat(),
            "duration_seconds": duration,
            "code_info": {
                "language": language,
                "description": description,
                "lines_of_code": len(code.split('\n')),
                "characters": len(code)
            },
            "agent_feedback": agent_feedback,
            "team_discussion": team_discussion,
            "final_summary": final_summary,
            "issues": issues,
            "recommendations": recommendations,
            "metrics": metrics,
            "conversation_history": conversation_history
        }
    
    def _extract_issues(self, agent_feedback: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Extract issues from agent feedback"""
        issues = []
        
        for agent, feedback_list in agent_feedback.items():
            for feedback in feedback_list:
                # Simple issue extraction (can be enhanced with NLP)
                if any(keyword in feedback.lower() for keyword in 
                      ['issue', 'problem', 'vulnerability', 'bug', 'error', 'critical']):
                    
                    # Determine severity based on keywords
                    severity = "Medium"
                    if any(word in feedback.lower() for word in ['critical', 'severe', 'high']):
                        severity = "Critical"
                    elif any(word in feedback.lower() for word in ['security', 'vulnerability']):
                        severity = "High"
                    elif any(word in feedback.lower() for word in ['minor', 'low', 'suggestion']):
                        severity = "Low"
                    
                    issues.append({
                        "agent": agent,
                        "severity": severity,
                        "description": feedback[:200] + "..." if len(feedback) > 200 else feedback,
                        "category": self._categorize_issue(agent, feedback)
                    })
        
        return issues
    
    def _extract_recommendations(self, agent_feedback: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Extract recommendations from agent feedback"""
        recommendations = []
        
        for agent, feedback_list in agent_feedback.items():
            for feedback in feedback_list:
                # Simple recommendation extraction
                if any(keyword in feedback.lower() for keyword in 
                      ['recommend', 'suggest', 'should', 'consider', 'improve']):
                    
                    recommendations.append({
                        "agent": agent,
                        "recommendation": feedback[:300] + "..." if len(feedback) > 300 else feedback,
                        "category": self._categorize_issue(agent, feedback)
                    })
        
        return recommendations
    
    def _categorize_issue(self, agent: str, content: str) -> str:
        """Categorize issue based on agent and content"""
        agent_categories = {
            "Senior_Developer": "Architecture & Design",
            "Security_Expert": "Security",
            "Performance_Analyst": "Performance", 
            "Documentation_Specialist": "Documentation"
        }
        return agent_categories.get(agent, "General")
    
    def _calculate_review_metrics(self, conversation_history: List[Dict], 
                                issues: List[Dict]) -> Dict[str, Any]:
        """Calculate review metrics"""
        
        # Count messages by agent
        agent_participation = {}
        for message in conversation_history:
            speaker = message.get("speaker", "Unknown")
            agent_participation[speaker] = agent_participation.get(speaker, 0) + 1
        
        # Count issues by severity
        severity_counts = {}
        for issue in issues:
            severity = issue.get("severity", "Unknown")
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Calculate overall score (simple heuristic)
        total_issues = len(issues)
        critical_issues = severity_counts.get("Critical", 0)
        high_issues = severity_counts.get("High", 0)
        
        # Score from 1-10 (10 being best)
        if critical_issues > 0:
            overall_score = max(1, 5 - critical_issues)
        elif high_issues > 2:
            overall_score = max(3, 7 - high_issues)
        elif total_issues > 5:
            overall_score = max(5, 8 - (total_issues - 5))
        else:
            overall_score = min(10, 8 + (5 - total_issues))
        
        return {
            "total_messages": len(conversation_history),
            "agent_participation": agent_participation,
            "total_issues": total_issues,
            "issues_by_severity": severity_counts,
            "overall_score": overall_score,
            "review_quality": "Excellent" if overall_score >= 8 else 
                            "Good" if overall_score >= 6 else
                            "Needs Improvement" if overall_score >= 4 else "Poor"
        }
    
    def get_team_info(self) -> Dict[str, Any]:
        """Get information about the review team"""
        return {
            "team_members": get_agent_descriptions(),
            "total_reviews_conducted": len(self.review_history),
            "configuration": {
                "model": self.config.LLM_MODEL,
                "temperature": self.config.TEMPERATURE,
                "max_rounds": self.config.REVIEW_PROCESS["max_total_rounds"]
            }
        }
    
    def get_review_history(self) -> List[Dict[str, Any]]:
        """Get history of all reviews conducted"""
        return [
            {
                "review_id": review.get("review_id"),
                "timestamp": review.get("timestamp"),
                "language": review.get("code_info", {}).get("language"),
                "issues_found": review.get("metrics", {}).get("total_issues", 0),
                "overall_score": review.get("metrics", {}).get("overall_score", 0)
            }
            for review in self.review_history
        ]
    
    def save_review_report(self, review_result: Dict[str, Any], 
                          filename: Optional[str] = None) -> str:
        """Save review report to file"""
        if not filename:
            review_id = review_result.get("review_id", "unknown")
            filename = f"./review_reports/{review_id}_report.json"
        
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w') as f:
            json.dump(review_result, f, indent=2)
        
        logger.info(f"Review report saved to {filename}")
        return filename
