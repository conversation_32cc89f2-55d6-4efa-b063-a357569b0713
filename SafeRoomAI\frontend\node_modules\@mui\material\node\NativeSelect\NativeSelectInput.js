"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = exports.StyledSelectSelect = exports.StyledSelectIcon = void 0;
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _clsx = _interopRequireDefault(require("clsx"));
var _refType = _interopRequireDefault(require("@mui/utils/refType"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _capitalize = _interopRequireDefault(require("../utils/capitalize"));
var _nativeSelectClasses = _interopRequireWildcard(require("./nativeSelectClasses"));
var _zeroStyled = require("../zero-styled");
var _rootShouldForwardProp = _interopRequireDefault(require("../styles/rootShouldForwardProp"));
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = ownerState => {
  const {
    classes,
    variant,
    disabled,
    multiple,
    open,
    error
  } = ownerState;
  const slots = {
    select: ['select', variant, disabled && 'disabled', multiple && 'multiple', error && 'error'],
    icon: ['icon', `icon${(0, _capitalize.default)(variant)}`, open && 'iconOpen', disabled && 'disabled']
  };
  return (0, _composeClasses.default)(slots, _nativeSelectClasses.getNativeSelectUtilityClasses, classes);
};
const StyledSelectSelect = exports.StyledSelectSelect = (0, _zeroStyled.styled)('select')(({
  theme
}) => ({
  // Reset
  MozAppearance: 'none',
  // Reset
  WebkitAppearance: 'none',
  // When interacting quickly, the text can end up selected.
  // Native select can't be selected either.
  userSelect: 'none',
  // Reset
  borderRadius: 0,
  cursor: 'pointer',
  '&:focus': {
    // Reset Chrome style
    borderRadius: 0
  },
  [`&.${_nativeSelectClasses.default.disabled}`]: {
    cursor: 'default'
  },
  '&[multiple]': {
    height: 'auto'
  },
  '&:not([multiple]) option, &:not([multiple]) optgroup': {
    backgroundColor: (theme.vars || theme).palette.background.paper
  },
  variants: [{
    props: ({
      ownerState
    }) => ownerState.variant !== 'filled' && ownerState.variant !== 'outlined',
    style: {
      // Bump specificity to allow extending custom inputs
      '&&&': {
        paddingRight: 24,
        minWidth: 16 // So it doesn't collapse.
      }
    }
  }, {
    props: {
      variant: 'filled'
    },
    style: {
      '&&&': {
        paddingRight: 32
      }
    }
  }, {
    props: {
      variant: 'outlined'
    },
    style: {
      borderRadius: (theme.vars || theme).shape.borderRadius,
      '&:focus': {
        borderRadius: (theme.vars || theme).shape.borderRadius // Reset the reset for Chrome style
      },
      '&&&': {
        paddingRight: 32
      }
    }
  }]
}));
const NativeSelectSelect = (0, _zeroStyled.styled)(StyledSelectSelect, {
  name: 'MuiNativeSelect',
  slot: 'Select',
  shouldForwardProp: _rootShouldForwardProp.default,
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.select, styles[ownerState.variant], ownerState.error && styles.error, {
      [`&.${_nativeSelectClasses.default.multiple}`]: styles.multiple
    }];
  }
})({});
const StyledSelectIcon = exports.StyledSelectIcon = (0, _zeroStyled.styled)('svg')(({
  theme
}) => ({
  // We use a position absolute over a flexbox in order to forward the pointer events
  // to the input and to support wrapping tags..
  position: 'absolute',
  right: 0,
  // Center vertically, height is 1em
  top: 'calc(50% - .5em)',
  // Don't block pointer events on the select under the icon.
  pointerEvents: 'none',
  color: (theme.vars || theme).palette.action.active,
  [`&.${_nativeSelectClasses.default.disabled}`]: {
    color: (theme.vars || theme).palette.action.disabled
  },
  variants: [{
    props: ({
      ownerState
    }) => ownerState.open,
    style: {
      transform: 'rotate(180deg)'
    }
  }, {
    props: {
      variant: 'filled'
    },
    style: {
      right: 7
    }
  }, {
    props: {
      variant: 'outlined'
    },
    style: {
      right: 7
    }
  }]
}));
const NativeSelectIcon = (0, _zeroStyled.styled)(StyledSelectIcon, {
  name: 'MuiNativeSelect',
  slot: 'Icon',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [styles.icon, ownerState.variant && styles[`icon${(0, _capitalize.default)(ownerState.variant)}`], ownerState.open && styles.iconOpen];
  }
})({});

/**
 * @ignore - internal component.
 */
const NativeSelectInput = /*#__PURE__*/React.forwardRef(function NativeSelectInput(props, ref) {
  const {
    className,
    disabled,
    error,
    IconComponent,
    inputRef,
    variant = 'standard',
    ...other
  } = props;
  const ownerState = {
    ...props,
    disabled,
    variant,
    error
  };
  const classes = useUtilityClasses(ownerState);
  return /*#__PURE__*/(0, _jsxRuntime.jsxs)(React.Fragment, {
    children: [/*#__PURE__*/(0, _jsxRuntime.jsx)(NativeSelectSelect, {
      ownerState: ownerState,
      className: (0, _clsx.default)(classes.select, className),
      disabled: disabled,
      ref: inputRef || ref,
      ...other
    }), props.multiple ? null : /*#__PURE__*/(0, _jsxRuntime.jsx)(NativeSelectIcon, {
      as: IconComponent,
      ownerState: ownerState,
      className: classes.icon
    })]
  });
});
process.env.NODE_ENV !== "production" ? NativeSelectInput.propTypes = {
  /**
   * The option elements to populate the select with.
   * Can be some `<option>` elements.
   */
  children: _propTypes.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  /**
   * The CSS class name of the select element.
   */
  className: _propTypes.default.string,
  /**
   * If `true`, the select is disabled.
   */
  disabled: _propTypes.default.bool,
  /**
   * If `true`, the `select input` will indicate an error.
   */
  error: _propTypes.default.bool,
  /**
   * The icon that displays the arrow.
   */
  IconComponent: _propTypes.default.elementType.isRequired,
  /**
   * Use that prop to pass a ref to the native select element.
   * @deprecated
   */
  inputRef: _refType.default,
  /**
   * @ignore
   */
  multiple: _propTypes.default.bool,
  /**
   * Name attribute of the `select` or hidden `input` element.
   */
  name: _propTypes.default.string,
  /**
   * Callback fired when a menu item is selected.
   *
   * @param {object} event The event source of the callback.
   * You can pull out the new value by accessing `event.target.value` (string).
   */
  onChange: _propTypes.default.func,
  /**
   * The input value.
   */
  value: _propTypes.default.any,
  /**
   * The variant to use.
   */
  variant: _propTypes.default.oneOf(['standard', 'outlined', 'filled'])
} : void 0;
var _default = exports.default = NativeSelectInput;