import * as React from 'react';
import { BarElementSlotProps, BarElementSlots } from './BarElement';
import { BarItemIdentifier } from '../models';
import { BarLabelItemProps, BarLabelSlotProps, BarLabelSlots } from './BarLabel/BarLabelItem';
export interface BarPlotSlots extends BarElementSlots, BarLabelSlots {
}
export interface BarPlotSlotProps extends BarElementSlotProps, BarLabelSlotProps {
}
export interface BarPlotProps extends Pick<BarLabelItemProps, 'barLabel'> {
    /**
     * If `true`, animations are skipped.
     * @default undefined
     */
    skipAnimation?: boolean;
    /**
     * Callback fired when a bar item is clicked.
     * @param {React.MouseEvent<SVGElement, MouseEvent>} event The event source of the callback.
     * @param {BarItemIdentifier} barItemIdentifier The bar item identifier.
     */
    onItemClick?: (event: React.MouseEvent<SVGElement, MouseEvent>, barItemIdentifier: BarItemIdentifier) => void;
    /**
     * Defines the border radius of the bar element.
     */
    borderRadius?: number;
    /**
     * The props used for each component slot.
     * @default {}
     */
    slotProps?: BarPlotSlotProps;
    /**
     * Overridable component slots.
     * @default {}
     */
    slots?: BarPlotSlots;
}
/**
 * Demos:
 *
 * - [Bars](https://mui.com/x/react-charts/bars/)
 * - [Bar demonstration](https://mui.com/x/react-charts/bar-demo/)
 * - [Stacking](https://mui.com/x/react-charts/stacking/)
 *
 * API:
 *
 * - [BarPlot API](https://mui.com/x/api/charts/bar-plot/)
 */
declare function BarPlot(props: BarPlotProps): React.JSX.Element;
declare namespace BarPlot {
    var propTypes: any;
}
export { BarPlot };
