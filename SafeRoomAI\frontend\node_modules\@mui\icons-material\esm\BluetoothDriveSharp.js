"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M15 10H4.81l1.04-3H15V5H4.43L2 12v9h3v-2h12v2h3v-9h-3c-1.1 0-2-.9-2-2m-8.5 6c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16m9 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5"
}, "0"), /*#__PURE__*/_jsx("path", {
  d: "M22 3.85 19.15 1h-.5v3.79l-2.3-2.29-.7.7L18.44 6l-2.79 2.79.7.71 2.3-2.3V11h.5L22 8.14 19.85 6zm-2.35-.94.94.94-.94.94zm.94 5.23-.94.94V7.2z"
}, "1")], 'BluetoothDriveSharp');