define(['./_cb', './_getLength'], function (_cb, _getLength) {

  // Use a comparator function to figure out the smallest index at which
  // an object should be inserted so as to maintain order. Uses binary search.
  function sortedIndex(array, obj, iteratee, context) {
    iteratee = _cb(iteratee, context, 1);
    var value = iteratee(obj);
    var low = 0, high = _getLength(array);
    while (low < high) {
      var mid = Math.floor((low + high) / 2);
      if (iteratee(array[mid]) < value) low = mid + 1; else high = mid;
    }
    return low;
  }

  return sortedIndex;

});
