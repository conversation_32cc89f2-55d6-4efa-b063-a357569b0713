"use strict";
exports.ht = void 0;
var _index = require("./ht/_lib/formatDistance.cjs");
var _index2 = require("./ht/_lib/formatLong.cjs");
var _index3 = require("./ht/_lib/formatRelative.cjs");
var _index4 = require("./ht/_lib/localize.cjs");
var _index5 = require("./ht/_lib/match.cjs");

/**
 * @category Locales
 * @summary Haitian Creole locale.
 * @language Haitian Creole
 * @iso-639-2 hat
 * <AUTHOR> [@rmariuzzo](https://github.com/rmariuzzo)
 * <AUTHOR> [@watsongm24](https://github.com/watsongm24)
 */
const ht = (exports.ht = {
  code: "ht",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
