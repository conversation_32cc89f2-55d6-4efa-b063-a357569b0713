{"name": "@rollup/plugin-node-resolve", "version": "11.2.1", "publishConfig": {"access": "public"}, "description": "Locate and bundle third-party dependencies in node_modules", "license": "MIT", "repository": "rollup/plugins", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/node-resolve/#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "type": "commonjs", "exports": {"require": "./dist/cjs/index.js", "import": "./dist/es/index.js"}, "engines": {"node": ">= 10.0.0"}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm run test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm run build && pnpm run lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm run test -- --verbose && pnpm run test:ts", "lint": "pnpm run lint:js && pnpm run lint:docs && pnpm run lint:package", "lint:docs": "prettier --single-quote --arrow-parens avoid --trailing-comma none --write README.md", "lint:js": "eslint --fix --cache src test types --ext .js,.ts", "lint:package": "prettier --write package.json --plugin=prettier-plugin-package", "prebuild": "del-cli dist", "prepare": "pnpm run build", "prepublishOnly": "pnpm run lint && pnpm run test && pnpm run test:ts", "pretest": "pnpm run build", "test": "ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}, "files": ["dist", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "es2015", "npm", "modules"], "peerDependencies": {"rollup": "^1.20.0||^2.0.0"}, "dependencies": {"@rollup/pluginutils": "^3.1.0", "@types/resolve": "1.17.1", "builtin-modules": "^3.1.0", "deepmerge": "^4.2.2", "is-module": "^1.0.0", "resolve": "^1.19.0"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/plugin-transform-typescript": "^7.10.5", "@rollup/plugin-babel": "^5.1.0", "@rollup/plugin-commonjs": "^16.0.0", "@rollup/plugin-json": "^4.1.0", "es5-ext": "^0.10.53", "rollup": "^2.23.0", "source-map": "^0.7.3", "string-capitalize": "^1.0.1"}, "types": "types/index.d.ts", "ava": {"babel": {"compileEnhancements": false}, "files": ["!**/fixtures/**", "!**/helpers/**", "!**/recipes/**", "!**/types.ts"]}}