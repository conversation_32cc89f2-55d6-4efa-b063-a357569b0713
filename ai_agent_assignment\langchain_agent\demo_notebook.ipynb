{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# LangChain Research Paper Analyzer Demo\n", "\n", "This notebook demonstrates the LangChain Research Paper Analyzer Agent functionality.\n", "\n", "## Features:\n", "- 📄 PDF Processing and Analysis\n", "- 🤖 Question Answering about Research Content\n", "- 📝 Summary Generation\n", "- 🔍 Research Gap Identification\n", "- 📚 Citation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Installation\n", "\n", "First, let's install the required packages and set up the environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install langchain openai faiss-cpu pypdf2 python-dotenv tiktoken\n", "\n", "# Import required libraries\n", "import os\n", "import sys\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Add current directory to path\n", "current_dir = os.getcwd()\n", "if current_dir not in sys.path:\n", "    sys.path.append(current_dir)\n", "\n", "print(\"✅ Packages installed and environment set up!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Environment Configuration\n", "\n", "Set up your OpenAI API key. You can either:\n", "1. Create a `.env` file with `OPENAI_API_KEY=your_key_here`\n", "2. Set it directly in the cell below"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Option 1: Load from .env file\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "# Option 2: Set directly (uncomment and add your key)\n", "# os.environ[\"OPENAI_API_KEY\"] = \"your_openai_api_key_here\"\n", "\n", "# Verify API key is set\n", "if os.getenv(\"OPENAI_API_KEY\"):\n", "    print(\"✅ OpenAI API key is configured\")\n", "else:\n", "    print(\"❌ OpenAI API key not found. Please set OPENAI_API_KEY environment variable.\")\n", "    print(\"You can get an API key from: https://platform.openai.com/api-keys\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize the Research Paper Analyzer\n", "\n", "Let's create an instance of our LangChain Research Paper Analyzer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import our custom modules\n", "try:\n", "    from research_analyzer import ResearchPaperAnalyzer\n", "    from config import Config\n", "    from utils import DocumentProcessor, TextAnalyzer, FileManager\n", "    \n", "    print(\"✅ Successfully imported custom modules\")\n", "    \n", "    # Initialize the analyzer\n", "    analyzer = ResearchPaperAnalyzer()\n", "    print(\"✅ Research Paper Analyzer initialized successfully!\")\n", "    \n", "except ImportError as e:\n", "    print(f\"❌ Import error: {e}\")\n", "    print(\"Make sure you're running this notebook from the langchain_agent directory\")\n", "except Exception as e:\n", "    print(f\"❌ Error initializing analyzer: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Sample PDF for Testing\n", "\n", "For demonstration purposes, let's create a sample research paper or use an existing one."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check for sample papers\n", "sample_papers_dir = Path(\"./sample_papers\")\n", "sample_papers_dir.mkdir(exist_ok=True)\n", "\n", "# List available PDF files\n", "pdf_files = list(sample_papers_dir.glob(\"*.pdf\"))\n", "\n", "if pdf_files:\n", "    print(\"📄 Available PDF files:\")\n", "    for i, pdf_file in enumerate(pdf_files):\n", "        print(f\"  {i+1}. {pdf_file.name}\")\n", "    \n", "    # Use the first available PDF\n", "    sample_pdf_path = str(pdf_files[0])\n", "    print(f\"\\n✅ Using: {sample_pdf_path}\")\n", "else:\n", "    print(\"⚠️  No PDF files found in ./sample_papers/\")\n", "    print(\"\\nTo test the analyzer, please:\")\n", "    print(\"1. Download a research paper PDF from arXiv.org or Google Scholar\")\n", "    print(\"2. Place it in the ./sample_papers/ directory\")\n", "    print(\"3. Re-run this cell\")\n", "    \n", "    # For demo purposes, let's set a placeholder path\n", "    sample_pdf_path = \"./sample_papers/your_research_paper.pdf\"\n", "    print(f\"\\n📝 Placeholder path set: {sample_pdf_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load and Process Research Paper\n", "\n", "Now let's load a research paper and process it for analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the research paper\n", "if os.path.exists(sample_pdf_path):\n", "    print(f\"📄 Loading research paper: {sample_pdf_path}\")\n", "    \n", "    # Load the paper\n", "    success = analyzer.load_paper(sample_pdf_path)\n", "    \n", "    if success:\n", "        print(\"✅ Paper loaded successfully!\")\n", "        \n", "        # Get paper information\n", "        paper_info = analyzer.get_paper_info()\n", "        \n", "        print(\"\\n📊 Paper Information:\")\n", "        print(f\"  • File: {paper_info['file_path']}\")\n", "        print(f\"  • Size: {paper_info['file_stats']['size']}\")\n", "        print(f\"  • Chunks created: {paper_info['processing_info']['chunks_created']}\")\n", "        print(f\"  • Vector store ready: {paper_info['processing_info']['vector_store_ready']}\")\n", "        print(f\"  • QA chain ready: {paper_info['processing_info']['qa_chain_ready']}\")\n", "        \n", "        # Display metadata if available\n", "        metadata = paper_info.get('metadata', {})\n", "        if metadata:\n", "            print(\"\\n📋 Document Metadata:\")\n", "            for key, value in metadata.items():\n", "                print(f\"  • {key.title()}: {value}\")\n", "    else:\n", "        print(\"❌ Failed to load paper\")\nelse:\n", "    print(f\"❌ PDF file not found: {sample_pdf_path}\")\n", "    print(\"\\nPlease add a research paper PDF to the ./sample_papers/ directory\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Question Answering Demo\n", "\n", "Let's test the question-answering capabilities of our analyzer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test questions for the research paper\n", "test_questions = [\n", "    \"What is the main research question or objective of this paper?\",\n", "    \"What methodology was used in this research?\",\n", "    \"What are the key findings or results?\",\n", "    \"What are the limitations mentioned in this study?\",\n", "    \"What future work is suggested by the authors?\"\n", "]\n", "\n", "# Only run if paper is loaded\n", "if hasattr(analyzer, 'qa_chain') and analyzer.qa_chain:\n", "    print(\"🤖 Testing Question Answering Capabilities\\n\")\n", "    print(\"=\" * 60)\n", "    \n", "    for i, question in enumerate(test_questions, 1):\n", "        print(f\"\\n❓ Question {i}: {question}\")\n", "        print(\"-\" * 50)\n", "        \n", "        try:\n", "            response = analyzer.ask(question)\n", "            \n", "            print(f\"💬 Answer: {response['answer']}\")\n", "            print(f\"🎯 Confidence: {response['confidence']}\")\n", "            print(f\"📚 Sources found: {len(response['sources'])}\")\n", "            \n", "            # Show first source if available\n", "            if response['sources']:\n", "                print(f\"📖 Sample source: {response['sources'][0]['content'][:150]}...\")\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Error: {e}\")\n", "        \n", "        print(\"\\n\" + \"=\"*60)\nelse:\n", "    print(\"⚠️  No paper loaded. Please load a paper first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interactive Question Answering\n", "\n", "Ask your own questions about the research paper!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive question answering\n", "if hasattr(analyzer, 'qa_chain') and analyzer.qa_chain:\n", "    print(\"💬 Interactive Question Answering\")\n", "    print(\"Ask any question about the loaded research paper!\")\n", "    print(\"(Type 'quit' to stop)\\n\")\n", "    \n", "    # For <PERSON><PERSON><PERSON> notebook, we'll demonstrate with a sample question\n", "    # In a real interactive session, you could use input()\n", "    \n", "    sample_question = \"What are the main contributions of this research?\"\n", "    print(f\"🔍 Sample Question: {sample_question}\")\n", "    \n", "    try:\n", "        response = analyzer.ask(sample_question)\n", "        \n", "        print(f\"\\n💡 Answer:\")\n", "        print(response['answer'])\n", "        \n", "        print(f\"\\n📊 Response Details:\")\n", "        print(f\"  • Confidence: {response['confidence']}\")\n", "        print(f\"  • Sources used: {len(response['sources'])}\")\n", "        \n", "        if response['sources']:\n", "            print(f\"\\n📚 Sources:\")\n", "            for i, source in enumerate(response['sources'][:2], 1):\n", "                print(f\"  {i}. {source['content'][:200]}...\")\n", "                \n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")\nelse:\n", "    print(\"⚠️  No paper loaded. Please load a paper first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate Research Paper Summary\n", "\n", "Let's generate a comprehensive summary of the research paper."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate summary\n", "if hasattr(analyzer, 'documents') and analyzer.documents:\n", "    print(\"📝 Generating Research Paper Summary...\\n\")\n", "    \n", "    try:\n", "        summary_result = analyzer.generate_summary()\n", "        \n", "        if 'error' not in summary_result:\n", "            print(\"✅ Summary Generated Successfully!\\n\")\n", "            print(\"=\" * 60)\n", "            \n", "            # Display summary\n", "            print(\"📋 SUMMARY:\")\n", "            print(summary_result['summary'])\n", "            \n", "            # Display statistics\n", "            stats = summary_result.get('statistics', {})\n", "            print(f\"\\n📊 STATISTICS:\")\n", "            print(f\"  • Total chunks: {stats.get('total_chunks', 'N/A')}\")\n", "            print(f\"  • Token count: {stats.get('token_count', 'N/A')}\")\n", "            print(f\"  • Estimated pages: {stats.get('estimated_pages', 'N/A')}\")\n", "            print(f\"  • Citations found: {stats.get('citations_found', 'N/A')}\")\n", "            \n", "            # Display keywords\n", "            keywords = summary_result.get('keywords', [])\n", "            if keywords:\n", "                print(f\"\\n🔑 KEY TERMS:\")\n", "                print(f\"  {', '.join(keywords)}\")\n", "            \n", "            # Display file info\n", "            file_info = summary_result.get('file_info', {})\n", "            print(f\"\\n📁 FILE INFO:\")\n", "            print(f\"  • Path: {file_info.get('path', 'N/A')}\")\n", "            print(f\"  • Size: {file_info.get('size', 'N/A')}\")\n", "            \n", "        else:\n", "            print(f\"❌ Error generating summary: {summary_result['error']}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")\nelse:\n", "    print(\"⚠️  No paper loaded. Please load a paper first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Gap Analysis\n", "\n", "Identify potential research gaps and future opportunities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Research gap analysis\n", "if hasattr(analyzer, 'documents') and analyzer.documents:\n", "    print(\"🔍 Identifying Research Gaps and Opportunities...\\n\")\n", "    \n", "    try:\n", "        gaps_result = analyzer.identify_research_gaps()\n", "        \n", "        if 'error' not in gaps_result:\n", "            print(\"✅ Research Gap Analysis Completed!\\n\")\n", "            print(\"=\" * 60)\n", "            \n", "            print(\"🎯 RESEARCH GAPS & OPPORTUNITIES:\")\n", "            print(gaps_result['research_gaps'])\n", "            \n", "            print(f\"\\n📊 ANALYSIS SCOPE:\")\n", "            print(f\"  • {gaps_result.get('analysis_scope', 'Unknown scope')}\")\n", "            print(f\"  • Paper: {gaps_result.get('paper_title', 'Unknown title')}\")\n", "            \n", "        else:\n", "            print(f\"❌ Error in research gap analysis: {gaps_result['error']}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")\nelse:\n", "    print(\"⚠️  No paper loaded. Please load a paper first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Citation Analysis\n", "\n", "Analyze the citations and references in the research paper."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Citation analysis\n", "if hasattr(analyzer, 'documents') and analyzer.documents:\n", "    print(\"📚 Analyzing Citations and References...\\n\")\n", "    \n", "    try:\n", "        citation_result = analyzer.analyze_citations()\n", "        \n", "        if 'error' not in citation_result:\n", "            print(\"✅ Citation Analysis Completed!\\n\")\n", "            print(\"=\" * 60)\n", "            \n", "            print(\"📖 CITATION ANALYSIS:\")\n", "            print(citation_result['citation_analysis'])\n", "            \n", "            print(f\"\\n📊 CITATION STATISTICS:\")\n", "            print(f\"  • Total citations found: {citation_result.get('total_citations', 0)}\")\n", "            print(f\"  • Paper: {citation_result.get('paper_title', 'Unknown title')}\")\n", "            \n", "            # Show sample citations\n", "            citations = citation_result.get('citations_extracted', [])\n", "            if citations:\n", "                print(f\"\\n📋 SAMPLE CITATIONS:\")\n", "                for i, citation in enumerate(citations[:5], 1):\n", "                    print(f\"  {i}. {citation}\")\n", "                \n", "                if len(citations) > 5:\n", "                    print(f\"  ... and {len(citations) - 5} more citations\")\n", "            \n", "        else:\n", "            print(f\"❌ Error in citation analysis: {citation_result['error']}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error: {e}\")\nelse:\n", "    print(\"⚠️  No paper loaded. Please load a paper first.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Complete Analysis Report\n", "\n", "Generate a comprehensive analysis report combining all features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Complete analysis report\n", "if hasattr(analyzer, 'documents') and analyzer.documents:\n", "    print(\"📊 Generating Complete Analysis Report...\\n\")\n", "    print(\"=\" * 80)\n", "    print(\"🔬 RESEARCH PAPER ANALYSIS REPORT\")\n", "    print(\"=\" * 80)\n", "    \n", "    try:\n", "        # Get paper info\n", "        paper_info = analyzer.get_paper_info()\n", "        \n", "        print(f\"\\n📄 DOCUMENT INFORMATION:\")\n", "        print(f\"  • File: {paper_info['file_path']}\")\n", "        print(f\"  • Size: {paper_info['file_stats']['size']}\")\n", "        print(f\"  • Processing chunks: {paper_info['processing_info']['chunks_created']}\")\n", "        \n", "        # Quick analysis questions\n", "        analysis_questions = [\n", "            \"What is the main contribution of this research?\",\n", "            \"What methodology was used?\",\n", "            \"What are the key results?\"\n", "        ]\n", "        \n", "        print(f\"\\n🤖 QUICK ANALYSIS:\")\n", "        for i, question in enumerate(analysis_questions, 1):\n", "            try:\n", "                response = analyzer.ask(question)\n", "                print(f\"\\n  {i}. {question}\")\n", "                print(f\"     → {response['answer'][:200]}...\")\n", "            except:\n", "                print(f\"\\n  {i}. {question}\")\n", "                print(f\"     → Error processing question\")\n", "        \n", "        print(f\"\\n\" + \"=\" * 80)\n", "        print(\"✅ Analysis Report Complete!\")\n", "        print(\"\\n💡 You can now:\")\n", "        print(\"  • Ask specific questions about the paper\")\n", "        print(\"  • Generate detailed summaries\")\n", "        print(\"  • Identify research gaps\")\n", "        print(\"  • Analyze citations\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error generating report: {e}\")\nelse:\n", "    print(\"⚠️  No paper loaded. Please load a paper first.\")\n", "    print(\"\\n📝 To get started:\")\n", "    print(\"  1. Add a research paper PDF to ./sample_papers/\")\n", "    print(\"  2. Re-run the 'Load and Process Research Paper' cell\")\n", "    print(\"  3. Then run this analysis report cell\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "🎉 **Congratulations!** You've successfully tested the LangChain Research Paper Analyzer Agent.\n", "\n", "### What you can do next:\n", "\n", "1. **Try different research papers** - Add more PDFs to the `./sample_papers/` directory\n", "2. **Ask custom questions** - Modify the question cells to ask specific questions about your research\n", "3. **Explore the Streamlit interface** - Run `streamlit run app.py` for a web interface\n", "4. **Customize the agent** - Modify the prompts in `config.py` for different analysis styles\n", "\n", "### Other agents in this project:\n", "- **AutoGen Code Review Team** - Multi-agent code review system\n", "- **LlamaIndex Customer Support** - Knowledge base question answering\n", "\n", "### Resources:\n", "- [<PERSON><PERSON><PERSON><PERSON> Documentation](https://python.langchain.com/)\n", "- [OpenAI API Documentation](https://platform.openai.com/docs)\n", "- [Research Papers on arXiv](https://arxiv.org/)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}