### Changelog

All notable changes to this project will be documented in this file. Dates are displayed in UTC.

Generated by [`auto-changelog`](https://github.com/CookPete/auto-changelog).

#### [v2.4.0](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/compare/v2.3.0...v2.4.0)

- Updating versions & tests for development [`1d11967`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/1d11967cf3d5d984629536ad51e03c9670c573ad)
- Update dependencies, travis targets. [`31491e0`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/31491e01cf9360d2144ad656df1253e678af1407)
- Run prettier on test index. [`3bf9fa2`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/3bf9fa22977d6ad61da5db0246d866c5b3e8af39)
- Run prettier across index. [`f12aa57`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/f12aa577b7e2b932b28aa4e21555cdb12929c352)
- Automatically generate changelog [`dc7765e`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/dc7765edb1c090a65c60005f790639a13345315a)
- Add test case ensuring deeply nested folder case problems are caught [`198c7e1`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/198c7e1416f010fc4ee6c439319bbaf5aa960973)
- [#54] Unescape # in paths [`7d05a0a`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/7d05a0a0945b1ef438da711297c1ab64b674d814)
- Change the pathCache to be a Map. [`737dadd`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/737dadd4b02e377296c9cd2d7c02703e09bb771a)
- Changelog [`c662bc4`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/c662bc43a8066f2714be7ea1289570b14821e47b)
- Bump lodash from 4.17.15 to 4.17.19 [`08dec5d`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/08dec5da716fae307f99336de02fab63b15ae7a2)
- Credit [`30dcfc1`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/30dcfc1c1ae091b1717ab18c98806a01358f664a)

#### [v2.3.0](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/compare/v2.1.2...v2.3.0)

> 15 January 2020

- Testing updates [`#37`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/37)
- Use the before emit hook to check if files exist [`#36`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/36)
- Add configurable logger [`#38`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/issues/38)
- Linting and dependency updates. [`d4fc994`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/d4fc99450ae45c2b631a4b949f92d62794ff52cf)
- Update for Node 4 use to prevent major semver bump [`e400f17`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/e400f177f2414e183f03c271f9683a2006ee3f1e)
- Start using AutoChangelog [`c2b6ca1`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/c2b6ca1f271dbac1565f27ab4be2a3f2712eea28)
- Update tests to properly account for Webpack 5 changes [`d84f848`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/d84f848df707d68dc14595df86ff88920ea709ff)
- Remove Node 4 from Travis test as it is flaky. [`329d32d`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/329d32d9b4614f73fe121743aeee2dedbf14cae9)
- Add Travis support for Webpack 5 and Node 12. [`4e9b361`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/4e9b36135ef6a5f0b19f41cce45dc5b1a9431b8b)
- Update index.js [`bef726c`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/bef726cd4686569f3fc7c8b84ea8bd6836c03f8a)
- Fix for #40 [`1c8e615`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/1c8e615a52eeb180d140102e8c23a07a9ca40c60)

#### [v2.1.2](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/compare/v2.0.0...v2.1.2)

> 6 March 2018

- Add support for webpack 4 [`#31`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/31)
- Add license scan report and status [`#27`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/27)
- Merge FOSSA's badge [`be74649`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/be74649d64c70f8b79efe25c320e737c2ebef07a)
- Release 2.1.0 - No longer return our own error on file not found. [`fb1d636`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/fb1d63653bdc83edc25aada8b8022a4d12fa330a)
- Testing improvements. [`2abbfce`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/2abbfce8f963b48ad596f6f46065575216abd6a5)
- Release 2.1.1 - Properly support older versions of node [`aaef33f`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/aaef33fc265522c223150756f059216c183b679e)
- Add badges. [`d1563c7`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/d1563c769ccd288cebfb74e3de7b612103e9cf5a)
- Fix test. [`6f2519b`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/6f2519b11e3ad6a521ffeee248bfda372023c636)
- Fix badges. [`face6dc`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/face6dc4b5b6a0f9820d89e79cab308e2dede4b8)
- Update README.md [`5cfb570`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/5cfb5709682a572df21d36d181c90629d2355e98)

### [v2.0.0](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/compare/1.1.4...v2.0.0)

> 31 March 2017

- Use compiler filesystem [`#14`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/14)
- Add `files` to `package.json`. [`#15`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/15)
- Update README.md [`#13`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/13)

#### 1.1.4

> 6 September 2016

- Don't crash on folder deletion with tests [`#10`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/10)
- Normalize filenames to default Unicode Normalization Form (NFC) so th… [`#6`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/6)
- Working E2E Tests in a Demo Project [`#4`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/4)
- check for windows-style root paths [`#2`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/pull/2)
- Create a working React/Webpack project, with Jasmine unit tests that demonstrate the functionality of the plugin. [`de4c1e5`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/de4c1e5cb9d7e8bcc229876735236c0043f5fdeb)
- Initial Code [`bb1d65b`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/bb1d65b0f7b1240a06e1f873ebf5a45f06b7d5bb)
- Drastically reduce filesystem operations required, release v1.1.0 [`ca03460`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/ca034602148adc7617e1edc39e7931f35de865b5)
- Added failing test when a folder is deleted while watching files [`e35eada`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/e35eadacc86cdcdaa864a4166a199b13ac852837)
- Initial commit [`e47ac63`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/e47ac63b7a914f90e912cd67967ade9866a3c543)
- Added a basic test [`293d43a`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/293d43abc8fc2217182eb8d3cdee5a826938f7af)
- More verbose output, add readme. [`961e350`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/961e350faec9c942c580b0a0d23917cda02eea8c)
- Handle situations where user has CDed into incorrectly cased directory [`d02babf`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/d02babf89f0bf4251efbd7ca5d64a79211487a74)
- 1.1.1 - Fix bug related to the cache and hot-reloading [`d287045`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/d2870450be2d33b8b9d4deb692e2fcf7d00d895c)
- Use @gaearon's method to stop crashes [`83ac4db`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/83ac4dbfe1288e09ba1a2971f3d9cb95779ba52c)
- Catch errors in plugin and propagate them [`e4fbf33`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/e4fbf333b624aa1543712967668f48ad8ea1d587)
- Credit where credit is due [`8124d95`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/8124d9545534fce33e3fd96aa9c4df18af00e329)
- update main README; [`52fc97c`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/52fc97c2464c78052bd41faeb2c7016f05205b40)
- Update README.md [`33db517`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/33db51747e6c20281de6497223bcabe1eba65994)
- Normalize filenames to default Unicode Normalization Form (NFC) so that all utf8-characters are compared correctly. [`41d9418`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/41d94181cc7f5ad52487b2e8058f82865eefe466)
- kill the recursive reference loop by directly accessing the package's `main` [`9f980f6`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/9f980f633dcb68829ff13e70ed72a9c70da688d5)
- Readme updates, bump version [`4be829d`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/4be829dd3bd39b1156ddbb6fbab9da794f8b6064)
- I know how markdown works, I swear [`5daec2c`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/5daec2cddfa4eda51c68e29e2adb5041e6e35f9e)
- Release 1.1.3 [`21390e1`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/21390e1d25ff9ed46229fb0541329e753a1f3410)
- readability edit [`99c0f0d`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/99c0f0de1a570ed8b67a9464bdf9287f24dce10e)
- Reduce waittime [`d0fd084`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/d0fd084236b231283191a4099c97584b2bba1676)
- Credit [`8818e92`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/8818e920f1c3f3df696489e89c08498389972ffa)
- remove the unnecessary require [`45bb0bb`](https://github.com/Urthen/case-sensitive-paths-webpack-plugin/commit/45bb0bbc5cca9137714bafca40ec8e351e2c4dbb)
