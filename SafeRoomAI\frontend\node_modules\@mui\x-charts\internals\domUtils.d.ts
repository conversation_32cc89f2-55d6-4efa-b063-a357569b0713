export declare const MEASUREMENT_SPAN_ID = "mui_measurement_span";
/**
 *
 * @param style React style object
 * @returns CSS styling string
 */
export declare const getStyleString: (style: React.CSSProperties) => string;
/**
 *
 * @param text The string to estimate
 * @param style The style applied
 * @returns width and height of the text
 */
export declare const getStringSize: (text: string | number, style?: React.CSSProperties) => any;
export declare function unstable_cleanupDOM(): void;
