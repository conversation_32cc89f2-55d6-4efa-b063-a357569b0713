# LangChain Research Paper Analyzer Agent

## Overview
This agent uses <PERSON><PERSON><PERSON><PERSON> to analyze academic research papers, extract key insights, and answer questions about the research content.

## Features
- **PDF Document Processing**: Upload and parse research papers
- **Citation Extraction**: Identify and extract citations from papers
- **Research Gap Analysis**: Identify potential research gaps and opportunities
- **Question Answering**: Ask questions about the research content
- **Summary Generation**: Create concise summaries of research findings
- **Semantic Search**: Find relevant sections based on queries

## Architecture
```
Research Paper Analyzer
├── Document Loader (PDF)
├── Text Splitter (Recursive)
├── Embedding Model (OpenAI)
├── Vector Store (FAISS)
├── Retrieval Chain (RAG)
└── LLM (GPT-3.5/4)
```

## Components

### 1. Document Processing Pipeline
- PDF text extraction using PyPDF2
- Text chunking with overlap for context preservation
- Metadata extraction (title, authors, abstract)

### 2. Vector Database
- FAISS vector store for efficient similarity search
- OpenAI embeddings for semantic understanding
- Persistent storage for processed documents

### 3. RAG Implementation
- Retrieval-Augmented Generation for accurate responses
- Context-aware question answering
- Source citation in responses

### 4. Analysis Tools
- Citation pattern analysis
- Research methodology identification
- Key findings extraction
- Research gap detection

## Usage

### Basic Usage
```python
from research_analyzer import ResearchPaperAnalyzer

# Initialize the analyzer
analyzer = ResearchPaperAnalyzer()

# Load a research paper
analyzer.load_paper("path/to/research_paper.pdf")

# Ask questions
response = analyzer.ask("What are the main findings of this research?")
print(response)

# Generate summary
summary = analyzer.generate_summary()
print(summary)

# Find research gaps
gaps = analyzer.identify_research_gaps()
print(gaps)
```

### Streamlit Interface
```bash
cd langchain_agent
streamlit run app.py
```

## Sample Research Papers
The `sample_papers/` directory contains example research papers for testing:
- AI/ML research papers
- Computer science papers
- Sample academic articles

## Demo Scenarios
1. **Paper Upload and Processing**
2. **Question Answering Demo**
3. **Citation Analysis**
4. **Research Gap Identification**
5. **Summary Generation**

## Files
- `research_analyzer.py`: Main agent implementation
- `app.py`: Streamlit web interface
- `utils.py`: Helper functions
- `config.py`: Configuration settings
- `sample_papers/`: Example research papers
- `tests/`: Unit tests
