import * as React from 'react';
import { ResponsiveChartContainerProps } from '../ResponsiveChartContainer';
import { ChartsTooltipProps, ChartsTooltipSlotProps, ChartsTooltipSlots } from '../ChartsTooltip';
import { ChartsAxisHighlightProps } from '../ChartsAxisHighlight';
import { AxisConfig, ChartsXAxisProps, ChartsYAxisProps, ScaleName } from '../models/axis';
import { MakeOptional } from '../models/helpers';
import { LineSeriesType } from '../models/seriesType';
import { CardinalDirections } from '../models/layout';
import { AreaPlotSlots, AreaPlotSlotProps } from '../LineChart/AreaPlot';
import { LinePlotSlots, LinePlotSlotProps } from '../LineChart/LinePlot';
import { MarkPlotSlots, MarkPlotSlotProps } from '../LineChart/MarkPlot';
import { LineHighlightPlotSlots, LineHighlightPlotSlotProps } from '../LineChart/LineHighlightPlot';
import { BarPlotSlots, BarPlotSlotProps } from '../BarChart/BarPlot';
export interface SparkLineChartSlots extends AreaPlotSlots, LinePlotSlots, MarkPlotSlots, LineHighlightPlotSlots, Omit<BarPlotSlots, 'barLabel'>, ChartsTooltipSlots<'line' | 'bar'> {
}
export interface SparkLineChartSlotProps extends AreaPlotSlotProps, LinePlotSlotProps, MarkPlotSlotProps, LineHighlightPlotSlotProps, BarPlotSlotProps, ChartsTooltipSlotProps<'line' | 'bar'> {
}
export interface SparkLineChartProps extends Omit<ResponsiveChartContainerProps, 'series' | 'xAxis' | 'yAxis' | 'zAxis' | 'margin' | 'plugins'> {
    /**
     * The xAxis configuration.
     * Notice it is a single [[AxisConfig]] object, not an array of configuration.
     */
    xAxis?: MakeOptional<AxisConfig<ScaleName, any, ChartsXAxisProps>, 'id'>;
    /**
     * The yAxis configuration.
     * Notice it is a single [[AxisConfig]] object, not an array of configuration.
     */
    yAxis?: MakeOptional<AxisConfig<ScaleName, any, ChartsYAxisProps>, 'id'>;
    tooltip?: ChartsTooltipProps<'line' | 'bar'>;
    axisHighlight?: ChartsAxisHighlightProps;
    /**
     * Type of plot used.
     * @default 'line'
     */
    plotType?: 'line' | 'bar';
    /**
     * Data to plot.
     */
    data: number[];
    /**
     * Formatter used by the tooltip.
     * @param {number} value The value to format.
     * @returns {string} the formatted value.
     * @default (value: number | null) => (value === null ? '' : value.toString())
     */
    valueFormatter?: (value: number | null) => string;
    /**
     * Set to `true` to enable the tooltip in the sparkline.
     * @default false
     */
    showTooltip?: boolean;
    /**
     * Set to `true` to highlight the value.
     * With line, it shows a point.
     * With bar, it shows a highlight band.
     * @default false
     */
    showHighlight?: boolean;
    /**
     * Set to `true` to fill spark line area.
     * Has no effect if plotType='bar'.
     * @default false
     */
    area?: LineSeriesType['area'];
    /**
     * @default 'linear'
     */
    curve?: LineSeriesType['curve'];
    /**
     * The margin between the SVG and the drawing area.
     * It's used for leaving some space for extra information such as the x- and y-axis or legend.
     * Accepts an object with the optional properties: `top`, `bottom`, `left`, and `right`.
     * @default {
     *   top: 5,
     *   bottom: 5,
     *   left: 5,
     *   right: 5,
     * }
     */
    margin?: Partial<CardinalDirections<number>>;
    /**
     * Overridable component slots.
     * @default {}
     */
    slots?: SparkLineChartSlots;
    /**
     * The props used for each component slot.
     * @default {}
     */
    slotProps?: SparkLineChartSlotProps;
}
/**
 * Demos:
 *
 * - [SparkLine](https://mui.com/x/react-charts/sparkline/)
 *
 * API:
 *
 * - [SparkLineChart API](https://mui.com/x/api/charts/spark-line-chart/)
 */
declare const SparkLineChart: React.ForwardRefExoticComponent<SparkLineChartProps & React.RefAttributes<unknown>>;
export { SparkLineChart };
