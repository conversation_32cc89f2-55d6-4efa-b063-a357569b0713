"use strict";
exports.th = void 0;
var _index = require("./th/_lib/formatDistance.cjs");
var _index2 = require("./th/_lib/formatLong.cjs");
var _index3 = require("./th/_lib/formatRelative.cjs");
var _index4 = require("./th/_lib/localize.cjs");
var _index5 = require("./th/_lib/match.cjs");

/**
 * @category Locales
 * @summary Thai locale.
 * @language Thai
 * @iso-639-2 tha
 * <AUTHOR> [@athivvat](https://github.com/athivvat)
 * <AUTHOR>
 * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)
 */
const th = (exports.th = {
  code: "th",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 0 /* Sunday */,
    firstWeekContainsDate: 1,
  },
});
