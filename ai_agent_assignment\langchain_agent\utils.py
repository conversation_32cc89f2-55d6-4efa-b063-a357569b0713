"""
Utility functions for the Research Paper Analyzer Agent
"""
import re
import os
import logging
from typing import List, Dict, Any
from PyPDF2 import PdfReader
import tiktoken

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """Utility class for document processing operations"""
    
    @staticmethod
    def extract_text_from_pdf(pdf_path: str) -> str:
        """
        Extract text content from a PDF file
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            str: Extracted text content
        """
        try:
            reader = PdfReader(pdf_path)
            text = ""
            
            for page in reader.pages:
                text += page.extract_text() + "\n"
            
            logger.info(f"Successfully extracted text from {pdf_path}")
            return text
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            raise
    
    @staticmethod
    def extract_metadata(pdf_path: str) -> Dict[str, Any]:
        """
        Extract metadata from a PDF file
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            Dict[str, Any]: Metadata dictionary
        """
        try:
            reader = PdfReader(pdf_path)
            metadata = reader.metadata
            
            return {
                'title': metadata.get('/Title', 'Unknown'),
                'author': metadata.get('/Author', 'Unknown'),
                'subject': metadata.get('/Subject', 'Unknown'),
                'creator': metadata.get('/Creator', 'Unknown'),
                'producer': metadata.get('/Producer', 'Unknown'),
                'creation_date': metadata.get('/CreationDate', 'Unknown'),
                'modification_date': metadata.get('/ModDate', 'Unknown'),
                'pages': len(reader.pages)
            }
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {e}")
            return {}

class TextAnalyzer:
    """Utility class for text analysis operations"""
    
    @staticmethod
    def count_tokens(text: str, model: str = "gpt-3.5-turbo") -> int:
        """
        Count the number of tokens in a text string
        
        Args:
            text (str): Input text
            model (str): Model name for tokenization
            
        Returns:
            int: Number of tokens
        """
        try:
            encoding = tiktoken.encoding_for_model(model)
            return len(encoding.encode(text))
        except Exception as e:
            logger.error(f"Error counting tokens: {e}")
            return 0
    
    @staticmethod
    def extract_citations(text: str) -> List[str]:
        """
        Extract citations from academic text
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of extracted citations
        """
        # Pattern for common citation formats
        patterns = [
            r'\([^)]*\d{4}[^)]*\)',  # (Author, 2023) format
            r'\[[^\]]*\d{4}[^\]]*\]',  # [Author, 2023] format
            r'\d{4}[a-z]?',  # Year format
        ]
        
        citations = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            citations.extend(matches)
        
        # Remove duplicates and return
        return list(set(citations))
    
    @staticmethod
    def extract_keywords(text: str, top_n: int = 10) -> List[str]:
        """
        Extract keywords from text (simple implementation)
        
        Args:
            text (str): Input text
            top_n (int): Number of top keywords to return
            
        Returns:
            List[str]: List of keywords
        """
        # Simple keyword extraction (can be enhanced with NLP libraries)
        words = re.findall(r'\b[a-zA-Z]{4,}\b', text.lower())
        
        # Common stop words to filter out
        stop_words = {
            'this', 'that', 'with', 'have', 'will', 'from', 'they', 'know',
            'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when',
            'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over',
            'such', 'take', 'than', 'them', 'well', 'were', 'what', 'your',
            'research', 'study', 'paper', 'analysis', 'method', 'approach',
            'results', 'conclusion', 'discussion', 'introduction', 'abstract'
        }
        
        # Filter stop words and count frequency
        filtered_words = [word for word in words if word not in stop_words]
        word_freq = {}
        
        for word in filtered_words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top N
        sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_words[:top_n]]

class FileManager:
    """Utility class for file management operations"""
    
    @staticmethod
    def validate_pdf(file_path: str) -> bool:
        """
        Validate if a file is a valid PDF
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            bool: True if valid PDF, False otherwise
        """
        try:
            if not os.path.exists(file_path):
                return False
            
            if not file_path.lower().endswith('.pdf'):
                return False
            
            # Try to read the PDF
            reader = PdfReader(file_path)
            return len(reader.pages) > 0
            
        except Exception:
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        Get file size in bytes
        
        Args:
            file_path (str): Path to the file
            
        Returns:
            int: File size in bytes
        """
        try:
            return os.path.getsize(file_path)
        except Exception:
            return 0
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        Format file size in human readable format
        
        Args:
            size_bytes (int): Size in bytes
            
        Returns:
            str: Formatted size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
