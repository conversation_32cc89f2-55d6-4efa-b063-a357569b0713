{"ast": null, "code": "import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport { default as THEME_ID } from \"./identifier.js\";\nexport { default as adaptV4Theme } from \"./adaptV4Theme.js\";\nexport { hexToRgb, rgbToHex, hslToRgb, decomposeColor, recomposeColor, getContrastRatio, getLuminance, emphasize, alpha, darken, lighten, css, keyframes } from '@mui/system';\nexport { unstable_createBreakpoints } from '@mui/system/createBreakpoints';\n// TODO: Remove this function in v6.\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function experimental_sx() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: The `experimental_sx` has been moved to `theme.unstable_sx`.' + 'For more details, see https://github.com/mui/material-ui/pull/35150.' : _formatMuiErrorMessage(19));\n}\nexport { default as createTheme, createMuiTheme } from \"./createTheme.js\";\nexport { default as unstable_createMuiStrictModeTheme } from \"./createMuiStrictModeTheme.js\";\nexport { default as createStyles } from \"./createStyles.js\";\nexport { getUnit as unstable_getUnit, toUnitless as unstable_toUnitless } from \"./cssUtils.js\";\nexport { default as responsiveFontSizes } from \"./responsiveFontSizes.js\";\nexport { default as createTransitions, duration, easing } from \"./createTransitions.js\";\nexport { default as createColorScheme } from \"./createColorScheme.js\";\nexport { default as useTheme } from \"./useTheme.js\";\nexport { default as useThemeProps } from \"./useThemeProps.js\";\nexport { default as styled } from \"./styled.js\";\nexport { default as experimentalStyled } from \"./styled.js\";\nexport { default as ThemeProvider } from \"./ThemeProvider.js\";\nexport { StyledEngineProvider } from '@mui/system';\n// The legacy utilities from @mui/styles\n// These are just empty functions that throws when invoked\nexport { default as makeStyles } from \"./makeStyles.js\";\nexport { default as withStyles } from \"./withStyles.js\";\nexport { default as withTheme } from \"./withTheme.js\";\nexport * from \"./ThemeProviderWithVars.js\";\nexport { default as extendTheme } from \"./createThemeWithVars.js\";\nexport { default as experimental_extendTheme } from \"./experimental_extendTheme.js\"; // TODO: Remove in v7\nexport { default as getOverlayAlpha } from \"./getOverlayAlpha.js\";\nexport { default as shouldSkipGeneratingVar } from \"./shouldSkipGeneratingVar.js\";\n\n// Private methods for creating parts of the theme\nexport { default as private_createTypography } from \"./createTypography.js\";\nexport { default as private_createMixins } from \"./createMixins.js\";\nexport { default as private_excludeVariablesFromRoot } from \"./excludeVariablesFromRoot.js\";", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "default", "THEME_ID", "adaptV4Theme", "hexToRgb", "rgbToHex", "hslToRgb", "decomposeColor", "recomposeColor", "getContrastRatio", "getLuminance", "emphasize", "alpha", "darken", "lighten", "css", "keyframes", "unstable_createBreakpoints", "experimental_sx", "Error", "process", "env", "NODE_ENV", "createTheme", "createMuiTheme", "unstable_createMuiStrictModeTheme", "createStyles", "getUnit", "unstable_getUnit", "toUni<PERSON>s", "unstable_toUnitless", "responsiveFontSizes", "createTransitions", "duration", "easing", "createColorScheme", "useTheme", "useThemeProps", "styled", "experimentalStyled", "ThemeProvider", "StyledEngineProvider", "makeStyles", "with<PERSON><PERSON><PERSON>", "withTheme", "extendTheme", "experimental_extendTheme", "getOverlayAlpha", "shouldSkipGeneratingVar", "private_createTypography", "private_createMixins", "private_excludeVariablesFromRoot"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/capstone final project/SafeRoomAI/frontend/node_modules/@mui/material/styles/index.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport { default as THEME_ID } from \"./identifier.js\";\nexport { default as adaptV4Theme } from \"./adaptV4Theme.js\";\nexport { hexToRgb, rgbToHex, hslToRgb, decomposeColor, recomposeColor, getContrastRatio, getLuminance, emphasize, alpha, darken, lighten, css, keyframes } from '@mui/system';\nexport { unstable_createBreakpoints } from '@mui/system/createBreakpoints';\n// TODO: Remove this function in v6.\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function experimental_sx() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? 'MUI: The `experimental_sx` has been moved to `theme.unstable_sx`.' + 'For more details, see https://github.com/mui/material-ui/pull/35150.' : _formatMuiErrorMessage(19));\n}\nexport { default as createTheme, createMuiTheme } from \"./createTheme.js\";\nexport { default as unstable_createMuiStrictModeTheme } from \"./createMuiStrictModeTheme.js\";\nexport { default as createStyles } from \"./createStyles.js\";\nexport { getUnit as unstable_getUnit, toUnitless as unstable_toUnitless } from \"./cssUtils.js\";\nexport { default as responsiveFontSizes } from \"./responsiveFontSizes.js\";\nexport { default as createTransitions, duration, easing } from \"./createTransitions.js\";\nexport { default as createColorScheme } from \"./createColorScheme.js\";\nexport { default as useTheme } from \"./useTheme.js\";\nexport { default as useThemeProps } from \"./useThemeProps.js\";\nexport { default as styled } from \"./styled.js\";\nexport { default as experimentalStyled } from \"./styled.js\";\nexport { default as ThemeProvider } from \"./ThemeProvider.js\";\nexport { StyledEngineProvider } from '@mui/system';\n// The legacy utilities from @mui/styles\n// These are just empty functions that throws when invoked\nexport { default as makeStyles } from \"./makeStyles.js\";\nexport { default as withStyles } from \"./withStyles.js\";\nexport { default as withTheme } from \"./withTheme.js\";\nexport * from \"./ThemeProviderWithVars.js\";\nexport { default as extendTheme } from \"./createThemeWithVars.js\";\nexport { default as experimental_extendTheme } from \"./experimental_extendTheme.js\"; // TODO: Remove in v7\nexport { default as getOverlayAlpha } from \"./getOverlayAlpha.js\";\nexport { default as shouldSkipGeneratingVar } from \"./shouldSkipGeneratingVar.js\";\n\n// Private methods for creating parts of the theme\nexport { default as private_createTypography } from \"./createTypography.js\";\nexport { default as private_createMixins } from \"./createMixins.js\";\nexport { default as private_excludeVariablesFromRoot } from \"./excludeVariablesFromRoot.js\";"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE,SAASC,OAAO,IAAIC,QAAQ,QAAQ,iBAAiB;AACrD,SAASD,OAAO,IAAIE,YAAY,QAAQ,mBAAmB;AAC3D,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,SAAS,QAAQ,aAAa;AAC7K,SAASC,0BAA0B,QAAQ,+BAA+B;AAC1E;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG,mEAAmE,GAAG,sEAAsE,GAAGtB,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACpO;AACA,SAASC,OAAO,IAAIsB,WAAW,EAAEC,cAAc,QAAQ,kBAAkB;AACzE,SAASvB,OAAO,IAAIwB,iCAAiC,QAAQ,+BAA+B;AAC5F,SAASxB,OAAO,IAAIyB,YAAY,QAAQ,mBAAmB;AAC3D,SAASC,OAAO,IAAIC,gBAAgB,EAAEC,UAAU,IAAIC,mBAAmB,QAAQ,eAAe;AAC9F,SAAS7B,OAAO,IAAI8B,mBAAmB,QAAQ,0BAA0B;AACzE,SAAS9B,OAAO,IAAI+B,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,wBAAwB;AACvF,SAASjC,OAAO,IAAIkC,iBAAiB,QAAQ,wBAAwB;AACrE,SAASlC,OAAO,IAAImC,QAAQ,QAAQ,eAAe;AACnD,SAASnC,OAAO,IAAIoC,aAAa,QAAQ,oBAAoB;AAC7D,SAASpC,OAAO,IAAIqC,MAAM,QAAQ,aAAa;AAC/C,SAASrC,OAAO,IAAIsC,kBAAkB,QAAQ,aAAa;AAC3D,SAAStC,OAAO,IAAIuC,aAAa,QAAQ,oBAAoB;AAC7D,SAASC,oBAAoB,QAAQ,aAAa;AAClD;AACA;AACA,SAASxC,OAAO,IAAIyC,UAAU,QAAQ,iBAAiB;AACvD,SAASzC,OAAO,IAAI0C,UAAU,QAAQ,iBAAiB;AACvD,SAAS1C,OAAO,IAAI2C,SAAS,QAAQ,gBAAgB;AACrD,cAAc,4BAA4B;AAC1C,SAAS3C,OAAO,IAAI4C,WAAW,QAAQ,0BAA0B;AACjE,SAAS5C,OAAO,IAAI6C,wBAAwB,QAAQ,+BAA+B,CAAC,CAAC;AACrF,SAAS7C,OAAO,IAAI8C,eAAe,QAAQ,sBAAsB;AACjE,SAAS9C,OAAO,IAAI+C,uBAAuB,QAAQ,8BAA8B;;AAEjF;AACA,SAAS/C,OAAO,IAAIgD,wBAAwB,QAAQ,uBAAuB;AAC3E,SAAShD,OAAO,IAAIiD,oBAAoB,QAAQ,mBAAmB;AACnE,SAASjD,OAAO,IAAIkD,gCAAgC,QAAQ,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}