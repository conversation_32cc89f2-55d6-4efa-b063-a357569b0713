"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ScopeType = void 0;
var ScopeType;
(function (ScopeType) {
    ScopeType["block"] = "block";
    ScopeType["catch"] = "catch";
    ScopeType["class"] = "class";
    ScopeType["classFieldInitializer"] = "class-field-initializer";
    ScopeType["classStaticBlock"] = "class-static-block";
    ScopeType["conditionalType"] = "conditionalType";
    ScopeType["for"] = "for";
    ScopeType["function"] = "function";
    ScopeType["functionExpressionName"] = "function-expression-name";
    ScopeType["functionType"] = "functionType";
    ScopeType["global"] = "global";
    ScopeType["mappedType"] = "mappedType";
    ScopeType["module"] = "module";
    ScopeType["switch"] = "switch";
    ScopeType["tsEnum"] = "tsEnum";
    ScopeType["tsModule"] = "tsModule";
    ScopeType["type"] = "type";
    ScopeType["with"] = "with";
})(ScopeType || (exports.ScopeType = ScopeType = {}));
//# sourceMappingURL=ScopeType.js.map