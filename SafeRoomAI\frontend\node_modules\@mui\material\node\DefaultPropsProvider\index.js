"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _DefaultPropsProvider.default;
  }
});
Object.defineProperty(exports, "useDefaultProps", {
  enumerable: true,
  get: function () {
    return _DefaultPropsProvider.useDefaultProps;
  }
});
var _DefaultPropsProvider = _interopRequireWildcard(require("./DefaultPropsProvider"));