{"version": 3, "sources": ["lib/locale/vi/cdn.js"], "sourcesContent": ["(() => {\nvar _window$dateFns;function _typeof(o) {\"@babel/helpers - typeof\";return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, \"string\");return \"symbol\" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if (\"object\" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || \"default\");if (\"object\" != _typeof(i)) return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return (\"string\" === r ? String : Number)(t);}var __defProp = Object.defineProperty;\nvar __export = function __export(target, all) {\n  for (var name in all)\n  __defProp(target, name, {\n    get: all[name],\n    enumerable: true,\n    configurable: true,\n    set: function set(newValue) {return all[name] = function () {return newValue;};}\n  });\n};\n\n// lib/locale/vi/_lib/formatDistance.js\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"d\\u01B0\\u1EDBi 1 gi\\xE2y\",\n    other: \"d\\u01B0\\u1EDBi {{count}} gi\\xE2y\"\n  },\n  xSeconds: {\n    one: \"1 gi\\xE2y\",\n    other: \"{{count}} gi\\xE2y\"\n  },\n  halfAMinute: \"n\\u1EEDa ph\\xFAt\",\n  lessThanXMinutes: {\n    one: \"d\\u01B0\\u1EDBi 1 ph\\xFAt\",\n    other: \"d\\u01B0\\u1EDBi {{count}} ph\\xFAt\"\n  },\n  xMinutes: {\n    one: \"1 ph\\xFAt\",\n    other: \"{{count}} ph\\xFAt\"\n  },\n  aboutXHours: {\n    one: \"kho\\u1EA3ng 1 gi\\u1EDD\",\n    other: \"kho\\u1EA3ng {{count}} gi\\u1EDD\"\n  },\n  xHours: {\n    one: \"1 gi\\u1EDD\",\n    other: \"{{count}} gi\\u1EDD\"\n  },\n  xDays: {\n    one: \"1 ng\\xE0y\",\n    other: \"{{count}} ng\\xE0y\"\n  },\n  aboutXWeeks: {\n    one: \"kho\\u1EA3ng 1 tu\\u1EA7n\",\n    other: \"kho\\u1EA3ng {{count}} tu\\u1EA7n\"\n  },\n  xWeeks: {\n    one: \"1 tu\\u1EA7n\",\n    other: \"{{count}} tu\\u1EA7n\"\n  },\n  aboutXMonths: {\n    one: \"kho\\u1EA3ng 1 th\\xE1ng\",\n    other: \"kho\\u1EA3ng {{count}} th\\xE1ng\"\n  },\n  xMonths: {\n    one: \"1 th\\xE1ng\",\n    other: \"{{count}} th\\xE1ng\"\n  },\n  aboutXYears: {\n    one: \"kho\\u1EA3ng 1 n\\u0103m\",\n    other: \"kho\\u1EA3ng {{count}} n\\u0103m\"\n  },\n  xYears: {\n    one: \"1 n\\u0103m\",\n    other: \"{{count}} n\\u0103m\"\n  },\n  overXYears: {\n    one: \"h\\u01A1n 1 n\\u0103m\",\n    other: \"h\\u01A1n {{count}} n\\u0103m\"\n  },\n  almostXYears: {\n    one: \"g\\u1EA7n 1 n\\u0103m\",\n    other: \"g\\u1EA7n {{count}} n\\u0103m\"\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", String(count));\n  }\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return result + \" n\\u1EEFa\";\n    } else {\n      return result + \" tr\\u01B0\\u1EDBc\";\n    }\n  }\n  return result;\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/vi/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, 'ng\\xE0y' d MMMM 'n\\u0103m' y\",\n  long: \"'ng\\xE0y' d MMMM 'n\\u0103m' y\",\n  medium: \"d MMM 'n\\u0103m' y\",\n  short: \"dd/MM/y\"\n};\nvar timeFormats = {\n  full: \"HH:mm:ss zzzz\",\n  long: \"HH:mm:ss z\",\n  medium: \"HH:mm:ss\",\n  short: \"HH:mm\"\n};\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};\n\n// lib/locale/vi/_lib/formatRelative.js\nvar formatRelativeLocale = {\n  lastWeek: \"eeee 'tu\\u1EA7n tr\\u01B0\\u1EDBc v\\xE0o l\\xFAc' p\",\n  yesterday: \"'h\\xF4m qua v\\xE0o l\\xFAc' p\",\n  today: \"'h\\xF4m nay v\\xE0o l\\xFAc' p\",\n  tomorrow: \"'ng\\xE0y mai v\\xE0o l\\xFAc' p\",\n  nextWeek: \"eeee 't\\u1EDBi v\\xE0o l\\xFAc' p\",\n  other: \"P\"\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return function (value, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : \"standalone\";\n    var valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/vi/_lib/localize.js\nvar eraValues = {\n  narrow: [\"TCN\", \"SCN\"],\n  abbreviated: [\"tr\\u01B0\\u1EDBc CN\", \"sau CN\"],\n  wide: [\"tr\\u01B0\\u1EDBc C\\xF4ng Nguy\\xEAn\", \"sau C\\xF4ng Nguy\\xEAn\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"Qu\\xFD 1\", \"Qu\\xFD 2\", \"Qu\\xFD 3\", \"Qu\\xFD 4\"]\n};\nvar formattingQuarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"qu\\xFD I\", \"qu\\xFD II\", \"qu\\xFD III\", \"qu\\xFD IV\"]\n};\nvar monthValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"10\", \"11\", \"12\"],\n  abbreviated: [\n  \"Thg 1\",\n  \"Thg 2\",\n  \"Thg 3\",\n  \"Thg 4\",\n  \"Thg 5\",\n  \"Thg 6\",\n  \"Thg 7\",\n  \"Thg 8\",\n  \"Thg 9\",\n  \"Thg 10\",\n  \"Thg 11\",\n  \"Thg 12\"],\n\n  wide: [\n  \"Th\\xE1ng M\\u1ED9t\",\n  \"Th\\xE1ng Hai\",\n  \"Th\\xE1ng Ba\",\n  \"Th\\xE1ng T\\u01B0\",\n  \"Th\\xE1ng N\\u0103m\",\n  \"Th\\xE1ng S\\xE1u\",\n  \"Th\\xE1ng B\\u1EA3y\",\n  \"Th\\xE1ng T\\xE1m\",\n  \"Th\\xE1ng Ch\\xEDn\",\n  \"Th\\xE1ng M\\u01B0\\u1EDDi\",\n  \"Th\\xE1ng M\\u01B0\\u1EDDi M\\u1ED9t\",\n  \"Th\\xE1ng M\\u01B0\\u1EDDi Hai\"]\n\n};\nvar formattingMonthValues = {\n  narrow: [\n  \"01\",\n  \"02\",\n  \"03\",\n  \"04\",\n  \"05\",\n  \"06\",\n  \"07\",\n  \"08\",\n  \"09\",\n  \"10\",\n  \"11\",\n  \"12\"],\n\n  abbreviated: [\n  \"thg 1\",\n  \"thg 2\",\n  \"thg 3\",\n  \"thg 4\",\n  \"thg 5\",\n  \"thg 6\",\n  \"thg 7\",\n  \"thg 8\",\n  \"thg 9\",\n  \"thg 10\",\n  \"thg 11\",\n  \"thg 12\"],\n\n  wide: [\n  \"th\\xE1ng 01\",\n  \"th\\xE1ng 02\",\n  \"th\\xE1ng 03\",\n  \"th\\xE1ng 04\",\n  \"th\\xE1ng 05\",\n  \"th\\xE1ng 06\",\n  \"th\\xE1ng 07\",\n  \"th\\xE1ng 08\",\n  \"th\\xE1ng 09\",\n  \"th\\xE1ng 10\",\n  \"th\\xE1ng 11\",\n  \"th\\xE1ng 12\"]\n\n};\nvar dayValues = {\n  narrow: [\"CN\", \"T2\", \"T3\", \"T4\", \"T5\", \"T6\", \"T7\"],\n  short: [\"CN\", \"Th 2\", \"Th 3\", \"Th 4\", \"Th 5\", \"Th 6\", \"Th 7\"],\n  abbreviated: [\"CN\", \"Th\\u1EE9 2\", \"Th\\u1EE9 3\", \"Th\\u1EE9 4\", \"Th\\u1EE9 5\", \"Th\\u1EE9 6\", \"Th\\u1EE9 7\"],\n  wide: [\n  \"Ch\\u1EE7 Nh\\u1EADt\",\n  \"Th\\u1EE9 Hai\",\n  \"Th\\u1EE9 Ba\",\n  \"Th\\u1EE9 T\\u01B0\",\n  \"Th\\u1EE9 N\\u0103m\",\n  \"Th\\u1EE9 S\\xE1u\",\n  \"Th\\u1EE9 B\\u1EA3y\"]\n\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"n\\u1EEDa \\u0111\\xEAm\",\n    noon: \"tr\",\n    morning: \"sg\",\n    afternoon: \"ch\",\n    evening: \"t\\u1ED1i\",\n    night: \"\\u0111\\xEAm\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"n\\u1EEDa \\u0111\\xEAm\",\n    noon: \"tr\\u01B0a\",\n    morning: \"s\\xE1ng\",\n    afternoon: \"chi\\u1EC1u\",\n    evening: \"t\\u1ED1i\",\n    night: \"\\u0111\\xEAm\"\n  },\n  wide: {\n    am: \"SA\",\n    pm: \"CH\",\n    midnight: \"n\\u1EEDa \\u0111\\xEAm\",\n    noon: \"tr\\u01B0a\",\n    morning: \"s\\xE1ng\",\n    afternoon: \"chi\\u1EC1u\",\n    evening: \"t\\u1ED1i\",\n    night: \"\\u0111\\xEAm\"\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: \"am\",\n    pm: \"pm\",\n    midnight: \"n\\u1EEDa \\u0111\\xEAm\",\n    noon: \"tr\",\n    morning: \"sg\",\n    afternoon: \"ch\",\n    evening: \"t\\u1ED1i\",\n    night: \"\\u0111\\xEAm\"\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"n\\u1EEDa \\u0111\\xEAm\",\n    noon: \"tr\\u01B0a\",\n    morning: \"s\\xE1ng\",\n    afternoon: \"chi\\u1EC1u\",\n    evening: \"t\\u1ED1i\",\n    night: \"\\u0111\\xEAm\"\n  },\n  wide: {\n    am: \"SA\",\n    pm: \"CH\",\n    midnight: \"n\\u1EEDa \\u0111\\xEAm\",\n    noon: \"gi\\u1EEFa tr\\u01B0a\",\n    morning: \"v\\xE0o bu\\u1ED5i s\\xE1ng\",\n    afternoon: \"v\\xE0o bu\\u1ED5i chi\\u1EC1u\",\n    evening: \"v\\xE0o bu\\u1ED5i t\\u1ED1i\",\n    night: \"v\\xE0o ban \\u0111\\xEAm\"\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, options) {\n  var number = Number(dirtyNumber);\n  var unit = options === null || options === void 0 ? void 0 : options.unit;\n  if (unit === \"quarter\") {\n    switch (number) {\n      case 1:\n        return \"I\";\n      case 2:\n        return \"II\";\n      case 3:\n        return \"III\";\n      case 4:\n        return \"IV\";\n    }\n  } else if (unit === \"day\") {\n    switch (number) {\n      case 1:\n        return \"th\\u1EE9 2\";\n      case 2:\n        return \"th\\u1EE9 3\";\n      case 3:\n        return \"th\\u1EE9 4\";\n      case 4:\n        return \"th\\u1EE9 5\";\n      case 5:\n        return \"th\\u1EE9 6\";\n      case 6:\n        return \"th\\u1EE9 7\";\n      case 7:\n        return \"ch\\u1EE7 nh\\u1EADt\";\n    }\n  } else if (unit === \"week\") {\n    if (number === 1) {\n      return \"th\\u1EE9 nh\\u1EA5t\";\n    } else {\n      return \"th\\u1EE9 \" + number;\n    }\n  } else if (unit === \"dayOfYear\") {\n    if (number === 1) {\n      return \"\\u0111\\u1EA7u ti\\xEAn\";\n    } else {\n      return \"th\\u1EE9 \" + number;\n    }\n  }\n  return String(number);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingQuarterValues,\n    defaultFormattingWidth: \"wide\",\n    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingMonthValues,\n    defaultFormattingWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n    return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n    return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\n\n// lib/locale/vi/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(tcn|scn)/i,\n  abbreviated: /^(trước CN|sau CN)/i,\n  wide: /^(trước Công Nguyên|sau Công Nguyên)/i\n};\nvar parseEraPatterns = {\n  any: [/^t/i, /^s/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^([1234]|i{1,3}v?)/i,\n  abbreviated: /^q([1234]|i{1,3}v?)/i,\n  wide: /^quý ([1234]|i{1,3}v?)/i\n};\nvar parseQuarterPatterns = {\n  any: [/(1|i)$/i, /(2|ii)$/i, /(3|iii)$/i, /(4|iv)$/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(0?[2-9]|10|11|12|0?1)/i,\n  abbreviated: /^thg[ _]?(0?[1-9](?!\\d)|10|11|12)/i,\n  wide: /^tháng ?(Một|Hai|Ba|Tư|Năm|Sáu|Bảy|Tám|Chín|Mười|Mười ?Một|Mười ?Hai|0?[1-9](?!\\d)|10|11|12)/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n  /0?1$/i,\n  /0?2/i,\n  /3/,\n  /4/,\n  /5/,\n  /6/,\n  /7/,\n  /8/,\n  /9/,\n  /10/,\n  /11/,\n  /12/],\n\n  abbreviated: [\n  /^thg[ _]?0?1(?!\\d)/i,\n  /^thg[ _]?0?2/i,\n  /^thg[ _]?0?3/i,\n  /^thg[ _]?0?4/i,\n  /^thg[ _]?0?5/i,\n  /^thg[ _]?0?6/i,\n  /^thg[ _]?0?7/i,\n  /^thg[ _]?0?8/i,\n  /^thg[ _]?0?9/i,\n  /^thg[ _]?10/i,\n  /^thg[ _]?11/i,\n  /^thg[ _]?12/i],\n\n  wide: [\n  /^tháng ?(Một|0?1(?!\\d))/i,\n  /^tháng ?(Hai|0?2)/i,\n  /^tháng ?(Ba|0?3)/i,\n  /^tháng ?(Tư|0?4)/i,\n  /^tháng ?(Năm|0?5)/i,\n  /^tháng ?(Sáu|0?6)/i,\n  /^tháng ?(Bảy|0?7)/i,\n  /^tháng ?(Tám|0?8)/i,\n  /^tháng ?(Chín|0?9)/i,\n  /^tháng ?(Mười|10)/i,\n  /^tháng ?(Mười ?Một|11)/i,\n  /^tháng ?(Mười ?Hai|12)/i]\n\n};\nvar matchDayPatterns = {\n  narrow: /^(CN|T2|T3|T4|T5|T6|T7)/i,\n  short: /^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,\n  abbreviated: /^(CN|Th ?2|Th ?3|Th ?4|Th ?5|Th ?6|Th ?7)/i,\n  wide: /^(Chủ ?Nhật|Chúa ?Nhật|thứ ?Hai|thứ ?Ba|thứ ?Tư|thứ ?Năm|thứ ?Sáu|thứ ?Bảy)/i\n};\nvar parseDayPatterns = {\n  narrow: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  short: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  abbreviated: [/CN/i, /2/i, /3/i, /4/i, /5/i, /6/i, /7/i],\n  wide: [/(Chủ|Chúa) ?Nhật/i, /Hai/i, /Ba/i, /Tư/i, /Năm/i, /Sáu/i, /Bảy/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(a|p|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n  abbreviated: /^(am|pm|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i,\n  wide: /^(ch[^i]*|sa|nửa đêm|trưa|(giờ) (sáng|chiều|tối|đêm))/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^(a|sa)/i,\n    pm: /^(p|ch[^i]*)/i,\n    midnight: /nửa đêm/i,\n    noon: /trưa/i,\n    morning: /sáng/i,\n    afternoon: /chiều/i,\n    evening: /tối/i,\n    night: /^đêm/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {return parseInt(value, 10);}\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: function valueCallback(index) {return index + 1;}\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"wide\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"wide\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/vi.js\nvar vi = {\n  code: \"vi\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// lib/locale/vi/cdn.js\nwindow.dateFns = _objectSpread(_objectSpread({},\nwindow.dateFns), {}, {\n  locale: _objectSpread(_objectSpread({}, (_window$dateFns =\n  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {\n    vi: vi }) });\n\n\n\n//# debugId=99CB759AEAC8A3D464756E2164756E21\n\n//# sourceMappingURL=cdn.js.map\n})();"], "mappings": "AAAA,CAAC,IAAM,CACP,IAAI,EAAgB,SAAS,CAAO,CAAC,EAAG,CAA2B,OAAO,SAA+B,QAArB,mBAAkD,OAAO,UAA1B,iBAA8C,CAAC,EAAG,CAAC,cAAc,WAAe,CAAC,EAAG,CAAC,OAAO,UAA0B,QAArB,YAA+B,EAAE,cAAgB,QAAU,IAAM,OAAO,UAAY,gBAAkB,GAAK,EAAQ,CAAC,EAAG,SAAS,CAAO,CAAC,EAAG,EAAG,CAAC,IAAI,EAAI,OAAO,KAAK,CAAC,EAAE,GAAI,OAAO,sBAAuB,CAAC,IAAI,EAAI,OAAO,sBAAsB,CAAC,EAAE,IAAM,EAAI,EAAE,eAAgB,CAAC,EAAG,CAAC,OAAO,OAAO,yBAAyB,EAAG,CAAC,EAAE,WAAY,GAAI,EAAE,KAAK,MAAM,EAAG,CAAC,EAAG,OAAO,EAAG,SAAS,CAAa,CAAC,EAAG,CAAC,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAC,IAAI,EAAY,UAAU,IAAlB,KAAuB,UAAU,GAAK,CAAC,EAAE,EAAI,EAAI,EAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,gBAAiB,CAAC,EAAG,CAAC,EAAgB,EAAG,EAAG,EAAE,EAAE,EAAG,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI,EAAQ,OAAO,CAAC,CAAC,EAAE,gBAAiB,CAAC,EAAG,CAAC,OAAO,eAAe,EAAG,EAAG,OAAO,yBAAyB,EAAG,CAAC,CAAC,EAAG,EAAG,OAAO,EAAG,SAAS,CAAe,CAAC,EAAK,EAAK,EAAO,CAA2B,GAA1B,EAAM,EAAe,CAAG,EAAM,KAAO,EAAM,OAAO,eAAe,EAAK,EAAK,CAAE,MAAO,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,MAAU,GAAI,GAAO,EAAO,OAAO,EAAK,SAAS,CAAc,CAAC,EAAG,CAAC,IAAI,EAAI,EAAa,EAAG,QAAQ,EAAE,OAAmB,EAAQ,CAAC,GAArB,SAAyB,EAAI,OAAO,CAAC,EAAG,SAAS,CAAY,CAAC,EAAG,EAAG,CAAC,GAAgB,EAAQ,CAAC,GAArB,WAA2B,EAAG,OAAO,EAAE,IAAI,EAAI,EAAE,OAAO,aAAa,GAAe,IAAN,OAAS,CAAC,IAAI,EAAI,EAAE,KAAK,EAAG,GAAK,SAAS,EAAE,GAAgB,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAE,MAAM,IAAI,UAAU,8CAA8C,EAAG,OAAqB,IAAb,SAAiB,OAAS,QAAQ,CAAC,EAAG,IAAI,EAAY,OAAO,eACroD,YAAoB,CAAQ,CAAC,EAAQ,EAAK,CAC5C,QAAS,KAAQ,EACjB,EAAU,EAAQ,EAAM,CACtB,IAAK,EAAI,GACT,WAAY,GACZ,aAAc,GACd,aAAc,CAAG,CAAC,EAAU,CAAC,OAAO,EAAI,WAAiB,EAAG,CAAC,OAAO,GACtE,CAAC,GAIC,EAAuB,CACzB,iBAAkB,CAChB,IAAK,2BACL,MAAO,kCACT,EACA,SAAU,CACR,IAAK,YACL,MAAO,mBACT,EACA,YAAa,mBACb,iBAAkB,CAChB,IAAK,2BACL,MAAO,kCACT,EACA,SAAU,CACR,IAAK,YACL,MAAO,mBACT,EACA,YAAa,CACX,IAAK,yBACL,MAAO,gCACT,EACA,OAAQ,CACN,IAAK,aACL,MAAO,oBACT,EACA,MAAO,CACL,IAAK,YACL,MAAO,mBACT,EACA,YAAa,CACX,IAAK,0BACL,MAAO,iCACT,EACA,OAAQ,CACN,IAAK,cACL,MAAO,qBACT,EACA,aAAc,CACZ,IAAK,yBACL,MAAO,gCACT,EACA,QAAS,CACP,IAAK,aACL,MAAO,oBACT,EACA,YAAa,CACX,IAAK,yBACL,MAAO,gCACT,EACA,OAAQ,CACN,IAAK,aACL,MAAO,oBACT,EACA,WAAY,CACV,IAAK,sBACL,MAAO,6BACT,EACA,aAAc,CACZ,IAAK,sBACL,MAAO,6BACT,CACF,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAS,CAClE,IAAI,EACA,EAAa,EAAqB,GACtC,UAAW,IAAe,SACxB,EAAS,UACA,IAAU,EACnB,EAAS,EAAW,QAEpB,GAAS,EAAW,MAAM,QAAQ,YAAa,OAAO,CAAK,CAAC,EAE9D,GAAI,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UACpD,GAAI,EAAQ,YAAc,EAAQ,WAAa,EAC7C,OAAO,EAAS,gBAEhB,QAAO,EAAS,mBAGpB,OAAO,GAIT,SAAS,CAAiB,CAAC,EAAM,CAC/B,eAAgB,EAAG,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACjG,EAAQ,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACrD,EAAS,EAAK,QAAQ,IAAU,EAAK,QAAQ,EAAK,cACtD,OAAO,GAKX,IAAI,EAAc,CAChB,KAAM,sCACN,KAAM,gCACN,OAAQ,qBACR,MAAO,SACT,EACI,EAAc,CAChB,KAAM,gBACN,KAAM,aACN,OAAQ,WACR,MAAO,OACT,EACI,EAAkB,CACpB,KAAM,oBACN,KAAM,oBACN,OAAQ,oBACR,MAAO,mBACT,EACI,EAAa,CACf,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,SAAU,EAAkB,CAC1B,QAAS,EACT,aAAc,MAChB,CAAC,CACH,EAGI,EAAuB,CACzB,SAAU,mDACV,UAAW,+BACX,MAAO,+BACP,SAAU,gCACV,SAAU,kCACV,MAAO,GACT,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAW,EAAU,CAAC,OAAO,EAAqB,IAG7G,SAAS,CAAe,CAAC,EAAM,CAC7B,eAAgB,CAAC,EAAO,EAAS,CAC/B,IAAI,EAAU,IAAY,MAAQ,IAAiB,QAAK,EAAQ,QAAU,OAAO,EAAQ,OAAO,EAAI,aAChG,EACJ,GAAI,IAAY,cAAgB,EAAK,iBAAkB,CACrD,IAAI,EAAe,EAAK,wBAA0B,EAAK,aACnD,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAC9F,EAAc,EAAK,iBAAiB,IAAU,EAAK,iBAAiB,OAC/D,CACL,IAAI,EAAgB,EAAK,aACrB,EAAS,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACpG,EAAc,EAAK,OAAO,IAAW,EAAK,OAAO,GAEnD,IAAI,EAAQ,EAAK,iBAAmB,EAAK,iBAAiB,CAAK,EAAI,EACnE,OAAO,EAAY,IAKvB,IAAI,EAAY,CACd,OAAQ,CAAC,MAAO,KAAK,EACrB,YAAa,CAAC,qBAAsB,QAAQ,EAC5C,KAAM,CAAC,oCAAqC,uBAAuB,CACrE,EACI,EAAgB,CAClB,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,KAAM,KAAM,KAAM,IAAI,EACpC,KAAM,CAAC,WAAY,WAAY,WAAY,UAAU,CACvD,EACI,EAA0B,CAC5B,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,KAAM,KAAM,KAAM,IAAI,EACpC,KAAM,CAAC,WAAY,YAAa,aAAc,WAAW,CAC3D,EACI,EAAc,CAChB,OAAQ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,IAAI,EACtE,YAAa,CACb,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,SACA,SACA,QAAQ,EAER,KAAM,CACN,oBACA,eACA,cACA,mBACA,oBACA,kBACA,oBACA,kBACA,mBACA,0BACA,mCACA,6BAA6B,CAE/B,EACI,EAAwB,CAC1B,OAAQ,CACR,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,IAAI,EAEJ,YAAa,CACb,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,SACA,SACA,QAAQ,EAER,KAAM,CACN,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,aAAa,CAEf,EACI,EAAY,CACd,OAAQ,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACjD,MAAO,CAAC,KAAM,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAM,EAC5D,YAAa,CAAC,KAAM,aAAc,aAAc,aAAc,aAAc,aAAc,YAAY,EACtG,KAAM,CACN,qBACA,eACA,cACA,mBACA,oBACA,kBACA,mBAAmB,CAErB,EACI,EAAkB,CACpB,OAAQ,CACN,GAAI,KACJ,GAAI,KACJ,SAAU,uBACV,KAAM,KACN,QAAS,KACT,UAAW,KACX,QAAS,WACT,MAAO,aACT,EACA,YAAa,CACX,GAAI,KACJ,GAAI,KACJ,SAAU,uBACV,KAAM,YACN,QAAS,UACT,UAAW,aACX,QAAS,WACT,MAAO,aACT,EACA,KAAM,CACJ,GAAI,KACJ,GAAI,KACJ,SAAU,uBACV,KAAM,YACN,QAAS,UACT,UAAW,aACX,QAAS,WACT,MAAO,aACT,CACF,EACI,EAA4B,CAC9B,OAAQ,CACN,GAAI,KACJ,GAAI,KACJ,SAAU,uBACV,KAAM,KACN,QAAS,KACT,UAAW,KACX,QAAS,WACT,MAAO,aACT,EACA,YAAa,CACX,GAAI,KACJ,GAAI,KACJ,SAAU,uBACV,KAAM,YACN,QAAS,UACT,UAAW,aACX,QAAS,WACT,MAAO,aACT,EACA,KAAM,CACJ,GAAI,KACJ,GAAI,KACJ,SAAU,uBACV,KAAM,sBACN,QAAS,2BACT,UAAW,8BACX,QAAS,4BACT,MAAO,wBACT,CACF,EACI,WAAyB,CAAa,CAAC,EAAa,EAAS,CAC/D,IAAI,EAAS,OAAO,CAAW,EAC3B,EAAO,IAAY,MAAQ,IAAiB,OAAS,OAAI,EAAQ,KACrE,GAAI,IAAS,UACX,OAAQ,OACD,GACH,MAAO,QACJ,GACH,MAAO,SACJ,GACH,MAAO,UACJ,GACH,MAAO,aAEF,IAAS,MAClB,OAAQ,OACD,GACH,MAAO,iBACJ,GACH,MAAO,iBACJ,GACH,MAAO,iBACJ,GACH,MAAO,iBACJ,GACH,MAAO,iBACJ,GACH,MAAO,iBACJ,GACH,MAAO,6BAEF,IAAS,OAClB,GAAI,IAAW,EACb,MAAO,yBAEP,OAAO,YAAc,UAEd,IAAS,YAClB,GAAI,IAAW,EACb,MAAO,4BAEP,OAAO,YAAc,EAGzB,OAAO,OAAO,CAAM,GAElB,EAAW,CACb,cAAe,EACf,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,QAAS,EAAgB,CACvB,OAAQ,EACR,aAAc,OACd,iBAAkB,EAClB,uBAAwB,OACxB,0BAA2B,CAAgB,CAAC,EAAS,CAAC,OAAO,EAAU,EACzE,CAAC,EACD,MAAO,EAAgB,CACrB,OAAQ,EACR,aAAc,OACd,iBAAkB,EAClB,uBAAwB,MAC1B,CAAC,EACD,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,UAAW,EAAgB,CACzB,OAAQ,EACR,aAAc,OACd,iBAAkB,EAClB,uBAAwB,MAC1B,CAAC,CACH,EAGA,SAAS,CAAY,CAAC,EAAM,CAC1B,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAQ,EAAQ,MAChB,EAAe,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC7E,EAAc,EAAO,MAAM,CAAY,EAC3C,IAAK,EACH,OAAO,KAET,IAAI,EAAgB,EAAY,GAC5B,EAAgB,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC9E,EAAM,MAAM,QAAQ,CAAa,EAAI,EAAU,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EAAI,EAAQ,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EACzM,EACJ,EAAQ,EAAK,cAAgB,EAAK,cAAc,CAAG,EAAI,EACvD,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,GAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,EAAK,GAGtC,SAAS,CAAO,CAAC,EAAQ,EAAW,CAClC,QAAS,KAAO,EACd,GAAI,OAAO,UAAU,eAAe,KAAK,EAAQ,CAAG,GAAK,EAAU,EAAO,EAAI,EAC5E,OAAO,EAGX,OAEF,SAAS,CAAS,CAAC,EAAO,EAAW,CACnC,QAAS,EAAM,EAAG,EAAM,EAAM,OAAQ,IACpC,GAAI,EAAU,EAAM,EAAI,EACtB,OAAO,EAGX,OAIF,SAAS,CAAmB,CAAC,EAAM,CACjC,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAgB,EAAY,GAC5B,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAQ,EAAK,cAAgB,EAAK,cAAc,EAAY,EAAE,EAAI,EAAY,GAClF,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAKtC,IAAI,EAA4B,UAC5B,EAA4B,OAC5B,EAAmB,CACrB,OAAQ,cACR,YAAa,sBACb,KAAM,uCACR,EACI,EAAmB,CACrB,IAAK,CAAC,MAAO,KAAK,CACpB,EACI,EAAuB,CACzB,OAAQ,sBACR,YAAa,uBACb,KAAM,yBACR,EACI,EAAuB,CACzB,IAAK,CAAC,UAAW,WAAY,YAAa,UAAU,CACtD,EACI,EAAqB,CACvB,OAAQ,2BACR,YAAa,qCACb,KAAM,+FACR,EACI,EAAqB,CACvB,OAAQ,CACR,QACA,OACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,KACA,KACA,IAAI,EAEJ,YAAa,CACb,sBACA,gBACA,gBACA,gBACA,gBACA,gBACA,gBACA,gBACA,gBACA,eACA,eACA,cAAc,EAEd,KAAM,CACN,2BACA,qBACA,oBACA,oBACA,qBACA,qBACA,qBACA,qBACA,sBACA,qBACA,0BACA,yBAAwB,CAE1B,EACI,EAAmB,CACrB,OAAQ,2BACR,MAAO,6CACP,YAAa,6CACb,KAAM,8EACR,EACI,EAAmB,CACrB,OAAQ,CAAC,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EAClD,MAAO,CAAC,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACjD,YAAa,CAAC,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,IAAI,EACvD,KAAM,CAAC,oBAAoB,OAAQ,MAAO,MAAO,OAAQ,OAAQ,MAAM,CACzE,EACI,EAAyB,CAC3B,OAAQ,kDACR,YAAa,oDACb,KAAM,wDACR,EACI,GAAyB,CAC3B,IAAK,CACH,GAAI,WACJ,GAAI,gBACJ,SAAU,WACV,KAAM,QACN,QAAS,QACT,UAAW,SACX,QAAS,OACT,MAAO,OACT,CACF,EACI,GAAQ,CACV,cAAe,EAAoB,CACjC,aAAc,EACd,aAAc,EACd,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,SAAS,EAAO,EAAE,EACzE,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,QAAS,EAAa,CACpB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACnB,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,EAAQ,EAC/D,CAAC,EACD,MAAO,EAAa,CAClB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACrB,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACrB,CAAC,EACD,UAAW,EAAa,CACtB,cAAe,EACf,kBAAmB,OACnB,cAAe,GACf,kBAAmB,KACrB,CAAC,CACH,EAGI,GAAK,CACP,KAAM,KACN,eAAgB,EAChB,WAAY,EACZ,eAAgB,EAChB,SAAU,EACV,MAAO,GACP,QAAS,CACP,aAAc,EACd,sBAAuB,CACzB,CACF,EAGA,OAAO,QAAU,EAAc,EAAc,CAAC,EAC9C,OAAO,OAAO,EAAG,CAAC,EAAG,CACnB,OAAQ,EAAc,EAAc,CAAC,GAAI,EACzC,OAAO,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,MAAM,EAAG,CAAC,EAAG,CAC7F,GAAI,EAAG,CAAC,CAAE,CAAC,IAOZ", "debugId": "6C2318370AAF54DE64756E2164756E21", "names": []}