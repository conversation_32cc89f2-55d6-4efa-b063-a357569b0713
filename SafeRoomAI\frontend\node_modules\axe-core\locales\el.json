{"lang": "el", "rules": {"accesskeys": {"description": "Διασφαλίζει ότι η τιμή κάθε χαρακτηριστικού accesskey είναι μοναδική", "help": "η τιμή κάθε χαρακτηριστικού accesskey πρέπει να είναι μοναδική"}, "area-alt": {"description": "Διασφαλίζει ότι τα στοιχεία <area> των χαρτών εικόνας έχουν εναλλακτικό κείμενο", "help": "Τα ενεργά στοιχεία <area> πρέπει να έχουν εναλλακτικό κείμενο"}, "aria-allowed-attr": {"description": "Διασφαλίζει ότι o ρόλος του στοιχείου υποστηρίζει τα χαρακτηριστικά ARIA", "help": "Τα στοιχεία πρέπει να χρησιμοποιούν μόνο υποστηριζόμενα χαρακτηριστικά ARIA"}, "aria-allowed-role": {"description": "Διασφαλίζει ότι το χαρακτηριστικό role έχει την κατάλληλη τιμή για το στοιχείο", "help": "Ο ρόλος ARIA θα πρέπει να είναι κατάλληλος για το στοιχείο"}, "aria-command-name": {"description": "Διασφαλί<PERSON><PERSON><PERSON> ότι κάθε button, link ή menuitem ARIA έχει ένα προσβάσιμο όνομα", "help": "Οι εντολές ARIA πρέπει να έχουν ένα προσβάσιμο όνομα"}, "aria-dialog-name": {"description": "Διασφαλίζ<PERSON><PERSON> ότι κάθε ARIA σε κόμβο διαλόγου ή διαλόγου ειδοποίησης έχει ένα προσβάσιμο όνομα", "help": "Οι ARIA κόμβοι διαλόγου καί διαλόγου ειδοποίησης πρέπει να έχουν ένα προσβάσιμο όνομα"}, "aria-hidden-body": {"description": "Διασφαλίζει ότι το aria-hidden='true' δεν υπάρχει στο σώμα (body) του εγγράφου.", "help": "Το aria-hidden='true' δεν πρέπει να υπάρχει στο σώμα (body) του εγγράφου."}, "aria-hidden-focus": {"description": "Διασφαλίζει ότι τα κρυφά στοιχεία aria δεν είναι εστιάσιμα ούτε περιέχουν στοιχεία που μπορούν να εστιαστούν", "help": "Το κρυφό στοιχείο ARIA δεν πρέπει να έχει δυνατότητα εστίασης ή να περιέχει στοιχεία με δυνατότητα εστίασης"}, "aria-input-field-name": {"description": "Διασφαλίζ<PERSON><PERSON> ότι κάθε πεδίο εισαγωγής ARIA έχει ένα προσβάσιμο όνομα", "help": "Τα πεδία εισαγωγής ARIA πρέπει να έχουν προσβάσιμο όνομα"}, "aria-meter-name": {"description": "Διασφαλ<PERSON><PERSON><PERSON><PERSON> ότι κάθε κόμβος μετρητή ARIA έχει ένα προσβάσιμο όνομα", "help": "Οι κόμβοι μετρητών ARIA πρέπει να έχουν προσβάσιμο όνομα"}, "aria-progressbar-name": {"description": "Διασφαλ<PERSON><PERSON><PERSON><PERSON> ότι κάθε κόμβος γραμμής προόδου ARIA έχει ένα προσβάσιμο όνομα", "help": "Οι κόμβοι γραμμής προόδου ARIA πρέπει να έχουν ένα προσβάσιμο όνομα"}, "aria-required-attr": {"description": "Διασφαλίζει ότι τα στοιχεία με ρόλους ARIA έχουν όλα τα απαιτούμενα χαρακτηριστικά ARIA", "help": "Πρέπει να παρέχονται τα απαιτούμενα χαρακτηριστικά ARIA"}, "aria-required-children": {"description": "Διασφαλίζει ότι τα στοιχεία με ρόλο ARIA που απαιτούν θυγατρικούς ρόλους περιέχουν τα στοιχεία", "help": "Ορισμένοι ρόλοι ARIA πρέπει να περιέχουν συγκεκριμένα παιδιά"}, "aria-required-parent": {"description": "Διασφαλίζει ότι τα στοιχεία με ρόλο ARIA που απαιτούν γονικούς ρόλους περιέχονται σε αυτά", "help": "Συγκεκριμένοι ρόλοι ARIA πρέπει να περιέχονται από συγκεκριμένους γονείς"}, "aria-roledescription": {"description": "Διασφαλίζει ότι το aria-roledescription χρησιμοποιείται μόνο σε στοιχεία με έμμεσο ή ρητό ρόλο", "help": "Το aria-roledescription πρέπει να είναι σε στοιχεία με σημασιολογικό ρόλο"}, "aria-roles": {"description": "Διασφαλίζ<PERSON><PERSON> ότι όλα τα στοιχεία με χαρακτηριστικό ρόλου χρησιμοποιούν μια έγκυρη τιμή", "help": "Οι ρόλοι ARIA που χρησιμοποιούνται πρέπει να συμμορφώνονται με έγκυρες τιμές"}, "aria-text": {"description": "Διασφαλίζει ότι το \"role=text\" χρησιμοποιείται σε στοιχεία χωρίς δυνατότητα εστίασης", "help": "Το \"role=text\" δεν πρέπει να έχει εστιάσιμους απογόνους"}, "aria-toggle-field-name": {"description": "Διασφαλίζ<PERSON><PERSON> ότι κάθε πεδίο εναλλαγής ARIA έχει ένα προσβάσιμο όνομα", "help": "Τα πεδία εναλλαγής ARIA πρέπει να έχουν προσβάσιμο όνομα"}, "aria-tooltip-name": {"description": "Διασφαλίζ<PERSON><PERSON> ότι κάθε κόμβος tooltip ARIA έχει ένα προσβάσιμο όνομα", "help": "Οι κόμβοι tooltip ARIA πρέπει να έχουν προσβάσιμο όνομα"}, "aria-treeitem-name": {"description": "Διασφαλίζ<PERSON>ι ότι κάθε κόμβος treeitem ARIA έχει ένα προσβάσιμο όνομα", "help": "Οι κόμβοι treeitem ARIA θα πρέπει να έχουν ένα προσβάσιμο όνομα"}, "aria-valid-attr-value": {"description": "Διασφαλί<PERSON><PERSON><PERSON> ότι όλα τα χαρακτηριστικ<PERSON> ARIA έχουν έγκυρες τιμές", "help": "Τα χαρακτηριστικ<PERSON> ARIA πρέπει να είναι σύμφωνα με έγκυρες τιμές"}, "aria-valid-attr": {"description": "Διασφαλίζει ότι τα χαρακτηριστικά που ξεκινούν με aria- είναι έγκυρα χαρακτηριστικά ARIA", "help": "Τα χαρακτηριστικ<PERSON> ARIA πρέπει να είναι σύμφωνα με έγκυρα ονόματα"}, "audio-caption": {"description": "Διασφαλίζει ότι τα στοιχεία <audio> έχουν υπότιτλους", "help": "Τα στοιχεία <audio> πρέπει να έχουν λεζάντες"}, "autocomplete-valid": {"description": "Διασφαλίζει ότι το χαρακτηριστικό autocomplete είναι σωστό και κατάλληλο για το πεδίο φόρμας", "help": "Το χαρακτηριστικό autocomplete πρέπει να χρησιμοποιείται σωστά"}, "avoid-inline-spacing": {"description": "Διασφαλίζει ότι η απόσταση κειμένου που έχει οριστεί μέσω χαρακτηριστικών στυλ μπορεί να προσαρμοστεί με προσαρμοσμένα φύλλα στυλ", "help": "Το ενσωματωμένο διάστημα κειμένου πρέπει να είναι ρυθμιζόμενο με προσαρμοσμένα φύλλα στυλ"}, "blink": {"description": "Διασφαλίζει ότι τα στοιχεία <blink> δεν χρησιμοποιούνται", "help": "Τα στοιχεία <blink> <PERSON>χ<PERSON>υν καταργηθεί και δεν πρέπει να χρησιμοποιούνται"}, "button-name": {"description": "Διασφαλίζει ότι τα κουμπιά έχουν ευδιάκριτο κείμενο", "help": "Τα κουμπιά πρέπει να έχουν ευδιάκριτο κείμενο"}, "bypass": {"description": "Διασφαλίζει ότι κάθε σελίδα έχει τουλάχιστον έναν μηχανισμό για τον χρήστη να παρακάμπτει την πλοήγηση και να μεταβαίνει απευθείας στο περιεχόμενο", "help": "Η σελίδα πρέπει να έχει τρόπο για την παράκαμψη επαναλαμβανόμενων μπλοκ"}, "color-contrast-enhanced": {"description": "Διασφαλίζει ότι η ενισχυμένη αντίθεση μεταξύ των χρωμάτων προσκηνίου και φόντου πληροί τα όρια αναλογίας αντίθεσης WCAG 2 AAA", "help": "Τα στοιχεία πρέπει να έχουν επαρκώς ενισχυμένη χρωματική αντίθεση"}, "color-contrast": {"description": "Διασφαλίζει ότι η αντίθεση μεταξύ των χρωμάτων προσκηνίου και φόντου πληροί τα όρια αναλογίας αντίθεσης WCAG 2 AA", "help": "Τα στοιχεία πρέπει να έχουν επαρκή χρωματική αντίθεση"}, "css-orientation-lock": {"description": "Διασφαλίζει ότι το περιεχόμενο δεν είναι κλειδωμένο σε κάποιον συγκεκριμένο προσανατολισμό οθόνης και ότι είναι λειτουργικό σε όλους τους προσανατολισμούς οθόνης", "help": "Τα ερωτήματα πολυμέσων CSS δεν πρέπει να κλειδώνουν τον προσανατολισμό της οθόνης"}, "definition-list": {"description": "Διασφαλίζει ότι τα στοιχεία <dl> έχουν δομηθεί σωστά", "help": "Τα στοιχεία <dl> πρέπει να περιέχουν μόνο σωστά διατεταγμένες ομάδες <dt> και <dd>, και <script>, <template> ή <div> στοιχεία"}, "dlitem": {"description": "Διασφαλίζει ότι τα στοιχεία <dt> και <dd> περιέχονται από ένα <dl>", "help": "Τα στοιχεία <dt> και <dd> πρέπει να περιέχονται από ένα <dl>"}, "document-title": {"description": "Διασφαλίζ<PERSON><PERSON> ότι κάθε έγγραφο HTML περιέχει ένα μη κενό στοιχείο <title>", "help": "Τα έγγραφα πρέπει να διαθέτουν το στοιχείο <title> για να βοηθήσουν στην πλοήγηση"}, "duplicate-id-active": {"description": "Διασφαλίζ<PERSON>ι ότι κάθε τιμή του χαρακτηριστικού id των ενεργών στοιχείων είναι μοναδική", "help": "Τα ID των ενεργών στοιχείων πρέπει να είναι μοναδικά"}, "duplicate-id-aria": {"description": "Διασφαλίζει ότι κάθε τιμή χαρακτηριστικού id που χρησιμοποιείται στο ARIA και στις ετικέτες είναι μοναδική", "help": "Τα ID που χρησιμοποιούνται στο ARIA και οι ετικέτες πρέπει να είναι μοναδικά"}, "duplicate-id": {"description": "Διασφαλίζ<PERSON><PERSON> ότι κάθε τιμή χαρακτηριστικού id είναι μοναδική", "help": "Η τιμή του χαρακτηριστικού id πρέπει να είναι μοναδική"}, "empty-heading": {"description": "Διασφαλίζει ότι οι επικεφαλίδες έχουν ευδιάκριτο κείμενο", "help": "Οι επικεφαλίδες δεν πρέπει να είναι κενές"}, "empty-table-header": {"description": "Διασφαλίζει ότι οι κεφαλίδες του πίνακα έχουν ευδιάκριτο κείμενο", "help": "Το κείμενο κεφαλίδας πίνακα δεν πρέπει να είναι κενό"}, "focus-order-semantics": {"description": "Διασφαλίζει ότι τα στοιχεία στη σειρά εστίασης έχουν ρόλο κατάλληλο για διαδραστικό περιεχόμενο", "help": "Τα στοιχεία στη σειρά εστίασης πρέπει να έχουν τον κατάλληλο ρόλο"}, "form-field-multiple-labels": {"description": "Διασφαλίζει ότι το πεδίο της φόρμας δεν έχει πολλαπλά στοιχεία ετικέτας", "help": "Το πεδίο φόρμας δεν πρέπει να έχει πολλαπλά στοιχεία ετικέτας"}, "frame-focusable-content": {"description": "Διασφαλίζει ότι τα στοιχεία <frame> και <iframe> με περιεχόμενο που μπορεί να εστιαστεί δεν έχουν tabindex=-1", "help": "Τα στοιχεία <frame> και <iframe> με εστιάσιμο περιεχόμενο δεν πρέπει να έχουν tabindex=-1"}, "frame-tested": {"description": "Διασφαλίζει ότι τα στοιχεία <iframe> και <frame> περιέχουν τη δέσμη ενεργειών του axe-core", "help": "Τα στοιχεία <iframe> και <frame πρέπει να ελέγχονται με το axe-core"}, "frame-title-unique": {"description": "Διασφαλίζει ότι τα στοιχεία <iframe> και <frame> περιέχουν ένα μοναδικό χαρακτηριστικό τίτλου", "help": "Τα πλαίσια πρέπει να έχουν ένα μοναδικό χαρακτηριστικό τίτλου"}, "frame-title": {"description": "Διασφαλίζει ότι τα στοιχεία <iframe> και <frame> έχουν ένα προσβάσιμο όνομα", "help": "Τα πλαίσια πρέπει να έχουν ένα προσβάσιμο όνομα"}, "heading-order": {"description": "Διασφαλίζει ότι η σειρά των επικεφαλίδων είναι σημασιολογικά σωστή", "help": "Τα επίπεδα επικεφαλίδων θα πρέπει να αυξάνονται μόνο κατά ένα"}, "hidden-content": {"description": "Ενημερώνει τους χρήστες για κρυφό περιεχόμενο.", "help": "Το κρυμμένο περιεχόμενο στη σελίδα πρέπει να αναλυθεί"}, "html-has-lang": {"description": "Διασφαλίζει ότι κάθε έγγραφο HTML έχει ένα χαρακτηριστικό lang", "help": "Το στοιχείο <html> πρέπει να έχει χαρακτηριστικό lang"}, "html-lang-valid": {"description": "Διασφαλίζει ότι το χαρακτηριστικό lang του στοιχείου <html> έχει έγκυρη τιμή", "help": "Το στοιχείο <html> πρέπει να έχει μια έγκυρη τιμή για το χαρακτηριστικό lang"}, "html-xml-lang-mismatch": {"description": "Διασφαλίζει ότι τα στοιχεία HTML με έγκυρα χαρακτηριστικά lang και xml:lang συμφωνούν στη βασική γλώσσα της σελίδας", "help": "Τα στοιχεία HTML με lang και xml:lang πρέπει να έχουν την ίδια βασική γλώσσα"}, "identical-links-same-purpose": {"description": "Διασφαλίζ<PERSON>ι ότι οι σύνδεσμοι με το ίδιο προσβάσιμο όνομα εξυπηρετούν παρόμοιο σκοπό", "help": "Οι σύνδεσμοι με το ίδιο όνομα πρέπει να έχουν παρόμοιο σκοπό"}, "image-alt": {"description": "Διασφαλίζει ότι τα στοιχεία <img> έχουν εναλλακτικό κείμενο ή κανένα ρόλο ή παρουσίαση", "help": "Οι εικόνες πρέπει να έχουν εναλλακτικό κείμενο"}, "image-redundant-alt": {"description": "Διασφαλίζει ότι το εναλλακτικό κείμενο δεν επαναλαμβάνεται", "help": "Το εναλλακ<PERSON>ι<PERSON><PERSON> κείμενο εικόνων δεν πρέπει να επαναλαμβάνεται"}, "input-button-name": {"description": "Διασφαλίζει ότι τα κουμπιά εισαγωγής έχουν ευδιάκριτο κείμενο", "help": "Τα κουμπιά εισαγωγής πρέπει να έχουν ευδιάκριτο κείμενο"}, "input-image-alt": {"description": "Διασφαλίζει ότι τα στοιχεία <input type=\"image\"> έχουν εναλλακτικό κείμενο", "help": "Τα κουμπιά εικόνας πρέπει να έχουν εναλλακτικό κείμενο"}, "label-content-name-mismatch": {"description": "Διασφαλίζει ότι τα στοιχεία που επισημαίνονται μέσω του περιεχομένου τους πρέπει να έχουν το ορατό κείμενό τους ως μέρος του προσβάσιμου ονόματος τους", "help": "Τα στοιχεία πρέπει να έχουν το ορατό κείμενό τους ως μέρος του προσβάσιμου ονόματός τους"}, "label-title-only": {"description": "Διασφαλίζει ότι κάθε στοιχεί<PERSON> φόρμας έχει ορατή ετικέτα και δεν επισημαίνεται αποκλειστικά με χρήση κρυφών ετικετών ή χαρακτηριστικών τίτλου ή άριας-describedby", "help": "Τα στοιχεία φόρμας πρέπει να έχουν ορατή ετικέτα"}, "label": {"description": "Διασφαλίζει ότι κάθε στοιχείο φόρμας έχει μια ετικέτα", "help": "Τα στοιχεία φόρμας πρέπει να έχουν ετικέτες"}, "landmark-banner-is-top-level": {"description": "Διασφαλίζ<PERSON>ι ότι το ορόσημο του banner βρίσκεται στο ανώτερο επίπεδο", "help": "Το ορόσημο του banner δεν θα πρέπει να περιέχεται σε άλλο ορόσημο"}, "landmark-complementary-is-top-level": {"description": "Εξασφαλίζει ότι το συμπληρωματικό ορόσημο ή το aside είναι στο κορυφαίο επίπεδο", "help": "Το aside δεν πρέπει να περιέχεται σε άλλο ορόσημο"}, "landmark-contentinfo-is-top-level": {"description": "Διασφαλίζει ότι το ορόσημο του contentinfo βρίσκεται στο ανώτατο επίπεδο", "help": "Το ορόσημο του contentinfo δεν πρέπει να περιέχεται σε άλλο ορόσημο"}, "landmark-main-is-top-level": {"description": "Διασφαλίζει ότι το ορόσημο main βρίσκεται σto κορυφα<PERSON><PERSON> επίπεδο", "help": "Το ορόσημο main δεν πρέπει να περιέχεται σε άλλο ορόσημο"}, "landmark-no-duplicate-banner": {"description": "Διασφαλίζει ότι το έγγραφο έχει το πολύ ένα ορόσημο banner", "help": "Το έγγραφο δεν πρέπει να έχει περισσότερα από ένα ορόσημα banner"}, "landmark-no-duplicate-contentinfo": {"description": "Διασφαλίζει ότι το έγγραφο έχει το πολύ ένα ορόσημο contentinfo", "help": "Το έγγραφο δεν πρέπει να έχει περισσότερα από ένα ορόσημα contentinfo"}, "landmark-no-duplicate-main": {"description": "Διασφαλίζει ότι το έγγραφο έχει το πολύ ένα ορόσημο main", "help": "Το έγγραφο δεν πρέπει να έχει περισσότερα από ένα ορόσημα main"}, "landmark-one-main": {"description": "Διασφαλίζει ότι το έγγραφο έχει ένα ορόσημο main", "help": "Το έγγραφο πρέπει να έχει ένα ορόσημο main"}, "landmark-unique": {"help": "Διασφαλ<PERSON>ζ<PERSON>ι ότι τα ορόσημα είναι μοναδικά", "description": "Τα ορόσημα πρέπει να έχουν μονα<PERSON><PERSON><PERSON><PERSON> ρόλο ή συνδυασμό ρόλου/ετικέτας/τίτλου (δηλαδή προσβάσιμο όνομα)."}, "link-in-text-block": {"description": "Διασφαλίζει ότι οι σύνδεσμοι διακρίνονται από το περιβάλλον κείμενο με τρόπο που δεν βασίζεται στο χρώμα", "help": "Οι σύνδεσμοι πρέπει να είναι διακριτοί χωρίς να βασίζονται στο χρώμα"}, "link-name": {"description": "Διασφαλίζ<PERSON>ι ότι οι σύνδεσμοι έχουν ευδιάκριτο κείμενο", "help": "Οι σύνδεσμοι πρέπει να έχουν ευδιάκριτο κείμενο"}, "list": {"description": "Διασφαλίζει ότι οι λίστες είναι σωστά δομημένες", "help": "Τα <ul> και <ol> πρέπει να περιέχουν μόνο στοιχεία <li>, <script> ή <template>"}, "listitem": {"description": "Διασφαλίζει ότι τα στοιχεία <li> χρησιμοποιούνται σημασιολογικά", "help": "Τα στοιχεία <li> πρέπει να περιέχονται σε <ul> ή <ol>"}, "marquee": {"description": "Διασφαλίζει ότι τα στοιχεία <marquee> δεν χρησιμοποιούνται", "help": "Τα στοιχεία <marquee> έχουν καταργηθεί και δεν πρέπει να χρησιμοποιούνται"}, "meta-refresh-no-exceptions": {"description": "Διασφαλίζ<PERSON>ι ότι το <meta http-equiv=\"refresh\"> δεν χρησιμοποιείται για καθυστερημένη ανανέωση", "help": "Δεν πρέπει να χρησιμοποιείται καθυστερημένη ανανέωση"}, "meta-refresh": {"description": "Διασφαλίζ<PERSON>ι ότι το <meta http-equiv=\"refresh\"> δεν χρησιμοποιείται για καθυστερημένη ανανέωση", "help": "Δεν πρέπει να χρησιμοποιείται καθυστερημένη ανανέωση κάτω των 20 ωρών"}, "meta-viewport-large": {"description": "Διασφαλίζ<PERSON>ι ότι το <meta name=\"viewport\"> μπορεί να κλιμακώσει σημαντικά", "help": "Οι χρήστες θα πρέπει να μπορούν να μεγεθύνουν και να κλιμακώσουν το κείμενο έως και 500%"}, "meta-viewport": {"description": "Διασφαλί<PERSON><PERSON><PERSON> ότι το <meta name=\"viewport\"> δεν απενεργοποιεί την κλιμάκωση και τη μεγέθυνση κειμένου", "help": "Το ζουμ και η κλιμάκωση δεν πρέπει να απενεργοποιούνται"}, "nested-interactive": {"description": "Διασφαλίζει ότι τα διαδραστι<PERSON><PERSON> στοιχεία ελέγχου δεν είναι ένθετα, καθώς δεν ανακοινώνονται πάντα από τα προγράμματα ανάγνωσης οθόνης ή μπορεί να προκαλέσουν προβλήματα εστίασης για τις υποστηρικτικές τεχνολογίες", "help": "Τα διαδραστι<PERSON><PERSON> στοιχεία ελέγχου δεν πρέπει να είναι ένθετα"}, "no-autoplay-audio": {"description": "Διασφαλίζει ότι τα στοιχεία <video> ή <audio> δεν αναπαράγουν αυτόματα ήχο για περισσότερα από 3 δευτερόλεπτα χωρίς μηχανισμό ελέγχου για διακοπή ή σίγαση του ήχου", "help": "Τα στοιχεία <video> ή <audio> δεν πρέπει να αναπαράγονται αυτόματα"}, "object-alt": {"description": "Διασφαλίζει ότι τα στοιχεία <object> έχουν εναλλακτικό κείμενο", "help": "Τα στοιχεία <object> πρέπει να έχουν εναλλακτικό κείμενο"}, "p-as-heading": {"description": "Διασφαλίζει ότι τα bold, italic και το font-size δε χρησιμοποιούνται για το στυλ των στοιχείων <p> ως επικεφαλίδα", "help": "Τα στοιχεία <p> με στυλ δεν πρέπει να χρησιμοποιούνται ως επικεφαλίδες"}, "page-has-heading-one": {"description": "Διασφαλίζει ότι η σελίδα ή τουλάχιστον ένα από τα frame της περιέχει μια επικεφαλίδα επιπέδου 1", "help": "Η σελίδα πρέπει να περιέχει μια επικεφαλίδα επιπέδου 1"}, "presentation-role-conflict": {"description": "Τα στοιχεία που έχουν επισημανθεί ως παρουσίασης δε θα πρέπει να έχουν καθολικό ARIA ή tabindex για να διασφαλίζεται ότι όλα τα προγράμματα ανάγνωσης οθόνης τα αγνοούν", "help": "Βεβαιωθείτε ότι τα στοιχεία που επισημαίνονται ως παρουσίασης οπωσδήποτε αγνοούνται"}, "region": {"description": "Εξασφαλί<PERSON><PERSON><PERSON> ότι όλο το περιεχόμενο της σελίδας περιλαμβάνεται από ορόσημα", "help": "Όλο το περιεχόμενο της σελίδας πρέπει να περιλαμβάνεται από ορόσημα"}, "role-img-alt": {"description": "Διασφαλίζει ότι τα στοιχεία [role='img'] έχουν εναλλακτικό κείμενο", "help": "Τα στοιχεία [role='img'] πρέπει να έχουν εναλλακτικό κείμενο"}, "scope-attr-valid": {"description": "Διασφαλίζει ότι το χαρακτηριστικό scope χρησιμοποιείται σωστά στους πίνακες", "help": "Το χαρακτηριστικό scope θα πρέπει να χρησιμοποιείται σωστά"}, "scrollable-region-focusable": {"description": "Διασφαλίζει ότι τα στοιχεία που έχουν περιεχόμενο με δυνατότητα κύλισης είναι προσβάσιμα από το πληκτρολόγιο", "help": "Η περιοχή με δυνατότητα κύλισης πρέπει να είναι προσβάσιμη από το πληκτρολόγιο"}, "select-name": {"description": "Διασφαλίζει ότι το στοιχεί<PERSON> select έχει ένα προσβάσιμο όνομα", "help": "Το στοιχ<PERSON><PERSON><PERSON> select πρέπει να έχει προσβάσιμο όνομα"}, "server-side-image-map": {"description": "Διασφαλίζει ότι δεν χρησιμοποιούνται χάρτες εικόνων από την πλευρά του διακομιστή", "help": "Δεν πρέπει να χρησιμοποιούνται χάρτες εικόνων από την πλευρά του διακομιστή"}, "skip-link": {"description": "Βεβαιωθείτε ότι όλοι οι σύνδεσμοι παράλειψης έχουν έναν στόχο που μπορεί να εστιαστεί", "help": "Ο στόχος παράβλεψης συνδέσμου θα πρέπει να υπάρχει και να μπορεί να εστιαστεί"}, "svg-img-alt": {"description": "Διασφαλίζει ότι τα στοιχεία <svg> με ρόλο img, graphics-document ή graphics-symbol έχουν προσβάσιμο κείμενο", "help": "Τα στοιχεία <svg> με ρόλο img πρέπει να έχουν εναλλακτικό κείμενο"}, "tabindex": {"description": "Διασφαλίζει ότι οι τιμές των χαρακτηριστικών tabindex δεν είναι μεγαλύτερες από 0", "help": "Τα στοιχεία δεν πρέπει να έχουν tabindex μεγαλύτερο από μηδέν"}, "table-duplicate-name": {"description": "Διασφαλίζει ότι το στοιχείο <caption> δεν περιέχει το ίδιο κείμενο με το χαρακτηριστικό summary", "help": "Οι πίνακες δεν πρέπει να έχουν την ίδια περίληψη και λεζάντα"}, "table-fake-caption": {"description": "Διασφαλ<PERSON>ζ<PERSON>ι ότι οι πίνακες με λεζάντα χρησιμοποιούν το στοιχείο <caption>", "help": "Δεδομέν<PERSON> ή κελιά κεφαλίδας δεν πρέπει να χρησιμοποιούνται για να δίνεται υπότιτλος σε έναν πίνακα δεδομένων"}, "target-size": {"description": "Διασφαλίζει ότι ο στόχος αφής έχει επαρκές μέγεθος και χώρο", "help": "Όλοι οι στόχοι αφής πρέπει να έχουν μέγεθος 24 px ή να αφήνουν αρκετό χώρο"}, "td-has-header": {"description": "Διασφαλίζει ότι κάθε μη κενό κελί δεδομένων σε ένα <table> μεγαλύτερο από 3 επί 3 έχει μία ή περισσότερες κεφαλίδες πίνακα", "help": "Τα μη κενά στοιχεία <td> σε μεγαλύτερο <table> πρέπει να έχουν συσχετισμένη κεφαλίδα πίνακα"}, "td-headers-attr": {"description": "Διασφαλίζει ότι κάθε κελί σε έναν πίνακα που χρησιμοποιεί το χαρακτηριστικό headers αναφέρεται μόνο σε άλλα κελιά σε αυτόν τον πίνακα", "help": "Τα κελιά πίνακα που χρησιμοποιούν το χαρακτηριστικό headers πρέπει να αναφέρονται μόνο σε κελιά του ίδιου πίνακα"}, "th-has-data-cells": {"description": "Διασφαλίζει ότι τα στοιχεία <th> και τα στοιχεία με role=columnheader/rowheader έχουν κελιά δεδομένων που περιγράφουν", "help": "Οι κεφαλίδες πίνακα σε έναν πίνακα δεδομένων πρέπει να παραπέμπουν σε κελιά δεδομένων"}, "valid-lang": {"description": "Διασφαλίζει ότι τα χαρακτηριστικ<PERSON> lang έχουν έγκυρες τιμές", "help": "Το χαρακτηριστικ<PERSON> lang πρέπει να έχει έγκυρη τιμή"}, "video-caption": {"description": "Διασφαλίζει ότι τα στοιχεία <video> έχουν υπότιτλους", "help": "Τα στοιχεία <video> πρέπει να έχουν υπότιτλους"}}, "checks": {"abstractrole": {"pass": "Δεν χρησιμοποιούνται αφηρημένοι ρόλοι", "fail": {"singular": "Ο αφηρημένος ρόλος δεν μπορεί να χρησιμοποιηθεί απευθείας: ${data.values}", "plural": "Οι αφηρημένοι ρόλοι δεν μπορούν να χρησιμοποιηθούν απευθείας: ${data.values}"}}, "aria-allowed-attr": {"pass": "Τα χαρακτηριστικ<PERSON> ARIA χρησιμοποιούνται σωστά για τον καθορισμένο ρόλο", "fail": {"singular": "Το χαρακτηριστικό ARIA δεν επιτρέπεται: ${data.values}", "plural": "Τα χαρακτηριστικά ARIA δεν επιτρέπονται: ${data.values}"}, "incomplete": "Βεβαιωθείτε ότι δεν υπάρχει πρόβλημα αν το χαρακτηριστικό ARIA αγνοηθεί σε αυτό το στοιχείο: ${data.values}"}, "aria-allowed-role": {"pass": "Ο ρόλος ARIA επιτρέπεται για το συγκεκριμένο στοιχείο", "fail": {"singular": "Ο ρόλος ARIA ${data.values} δεν επιτρέπεται για το συγκεκριμένο στοιχείο", "plural": "Οι ρόλοι ARIA ${data.values} δεν επιτρέπονται για το συγκεκριμένο στοιχείο"}, "incomplete": {"singular": "Ο ρόλος ARIA ${data.values} πρέπει να καταργηθεί όταν το στοιχείο γίνει ορατό, καθώς δεν επιτρέπεται για το στοιχείο", "plural": "Οι ρόλοι ARIA ${data.values} πρέπει να καταργηθούν όταν το στοιχείο γίνει ορατό, καθώς δεν επιτρέπονται για το στοιχείο"}}, "aria-busy": {"pass": "Το στοιχείο έχει το χαρακτηριστικό aria-busy", "fail": "Το στοιχείο δεν έχει το χαρακτηριστικό aria-busy=\"true\"."}, "aria-errormessage": {"pass": "Το aria-errormessage υπάρχει και παραπέμπει σε στοιχεία ορατά στους αναγνώστες οθόνης που χρησιμοποιούν μια υποστηριζόμενη τεχνική aria-errormessage", "fail": {"singular": "Η τιμή του aria-errormessage `${data.values}` πρέπει να χρησιμοποιεί μια τεχνική για να ανακοινώσει το μήνυμα (π.χ. aria-live, aria-describedby, role=alert, κ.λπ.)", "plural": "Οι τιμές του aria-errormessage `${data.values}` πρέπει να χρησιμοποιούν μια τεχνική για να ανακοινώσουν το μήνυμα (π.χ. aria-live, aria-describedby, role=alert, κ.λπ.)", "hidden": "Η τιμή του aria-errormessage `${data.values}` δεν μπορεί να αναφέρει ένα κρυφό στοιχείο"}, "incomplete": {"singular": "βεβαιωθείτε ότι η τιμή του aria-errormessage `${data.values}` αναφέρεται σε ένα υπάρχον στοιχείο", "plural": "βεβαιωθείτε ότι οι τιμές του aria-errormessage `${data.values}` αναφέρονται σε υπάρχοντα στοιχεία", "idrefs": "δεν είναι δυνατός ο προσδιορισμός του εάν το στοιχείο aria-errormessage υπάρχει στη σελίδα: ${data.values}"}}, "aria-hidden-body": {"pass": "Δεν υπάρχει χαρακτηριστικ<PERSON> aria-hidden στο σώμα του εγγράφου", "fail": "Το aria-hidden=true δεν πρέπει να υπάρχει στο σώμα του εγγράφου"}, "aria-level": {"pass": "Οι τιμές του aria-level είναι έγκυρες", "incomplete": "Τιμές του aria-level μεγαλύτερες από 6 δεν υποστηρίζονται σε όλους τους συνδυασμούς προγράμματος ανάγνωσης οθόνης και προγράμματος περιήγησης"}, "aria-prohibited-attr": {"pass": "Το χαρακτηριστικ<PERSON> ARIA επιτρέπεται", "fail": {"hasRolePlural": "Τα χαρακτηριστικά ${data.prohibited} δεν μπορούν να χρησιμοποιηθούν με το ρόλο \"${data.role}\".", "hasRoleSingular": "Το χαρακτηριστικό ${data.prohibited} δεν μπορεί να χρησιμοποιηθεί με το ρόλο \"${data.role}\".", "noRolePlural": "Τα χαρακτηριστικά ${data.prohibited} δεν μπορούν να χρησιμοποιηθούν σε ένα ${data.nodeName} χωρίς έγκυρο χαρακτηριστικό ρόλου.", "noRoleSingular": "Το χαρακτηριστικό ${data.prohibited} δεν μπορεί να χρησιμοποιηθεί σε ένα ${data.nodeName} χωρίς έγκυρο χαρακτηριστικό ρόλου."}, "incomplete": {"hasRoleSingular": "Το χαρακτηριστικό ${data.prohibited} δεν υποστηρίζεται καλά με τον ρόλο \"${data.role}\".", "hasRolePlural": "Τα χαρακτηριστικά ${data.prohibited} δεν υποστηρίζονται καλά με τον ρόλο \"${data.role}\".", "noRoleSingular": "Το χαρακτηριστικό ${data.prohibited} δεν υποστηρίζεται καλά σε ένα ${data.nodeName} χωρίς έγκυρο χαρακτηριστικό ρόλου.", "noRolePlural": "Τα χαρακτηριστικά ${data.prohibited} δεν υποστηρίζονται καλά σε ένα ${data.nodeName} χωρίς έγκυρο χαρακτηριστικό ρόλου."}}, "aria-required-attr": {"pass": "Υπάρχουν όλα τα απαιτούμενα χαρακτηριστικά ARIA", "fail": {"singular": "Δεν υπάρχει το απαιτούμενο χαρακτηριστικό ARIA: ${data.values}", "plural": "Δεν υπάρχουν τα απαιτούμενα χαρακτηριστικά ARIA: ${data.values}"}}, "aria-required-children": {"pass": {"default": "Οι απαιτούμενοι γόνοι ARIA είναι παρόντες"}, "fail": {"singular": "Ο απαιτούμενος θυγατρικός ρόλος ARIA δεν υπάρχει: ${data.values}", "plural": "Οι απαιτούμενοι θυγατρικόι ρόλοι ARIA δεν υπάρχουν: ${data.values}", "unallowed": "Το στοιχείο έχει γόνους που δεν επιτρέπονται ${data.values}"}, "incomplete": {"singular": "Αναμένεται προσθήκη θυγατρικού ρόλου ARIA: ${data.values}", "plural": "Αναμένεται προσθήκη θυγατρικών ρόλων ARIA: ${data.values}"}}, "aria-required-parent": {"pass": "Required ARIA parent role present", "fail": {"singular": "Ο απαιτούμενος γονικός ρόλος ARIA δεν υπάρχει: ${data.values}", "plural": "Οι απαιτούμενοι ρόλοι γονέα ARIA δεν υπάρχουν: ${data.values}"}}, "aria-roledescription": {"pass": "το aria-roledescription χρησιμοποιείται σε υποστηριζόμενο σημασιολογικό ρόλο", "incomplete": "Βεβαιωθείτε ότι το aria-roledescription ανακ<PERSON>ινώνεται από υποστηριζόμενα προγράμματα ανάγνωσης οθόνης", "fail": "Δώστε στο στοιχε<PERSON><PERSON> έναν ρόλο που υποστηρίζει το aria-roledescription"}, "aria-unsupported-attr": {"pass": "Το χαρακτηριστικ<PERSON> ARIA υποστηρίζεται", "fail": "Το χαρακτηριστικ<PERSON> ARIA δεν υποστηρίζεται ευρέως σε προγράμματα ανάγνωσης οθόνης και υποστηρικτικές τεχνολογίες: ${data.values}"}, "aria-valid-attr-value": {"pass": "Οι τιμές του χαρακτηριστικού ARIA είναι έγκυρες", "fail": {"singular": "Μη έγκυρη τιμή χαρακτηριστικού ARIA: ${data.values}", "plural": "Μη έγκυρες τιμές χαρακτηριστικών ARIA: ${data.values}"}, "incomplete": {"noId": "Το ID του χαρακτηριστικού ARIA δεν υπάρχει στη σελίδα: ${data.needsReview}", "noIdShadow": "Το ID του χαρακτηριστικού ARIA δεν υπάρχει στη σελίδα ή είναι απόγονος ενός διαφορετικού σκιώδους DOM: ${data.needsReview}", "ariaCurrent": "Η τιμή χαρακτηριστικού ARIA δεν είναι έγκυρη και θα αντιμετωπιστεί ως \"aria-current=true\": ${data.needsReview}", "idrefs": "Δεν είναι δυνατός ο προσδιορισμός εάν το ID του χαρακτηριστικού ARIA υπάρχει στη σελίδα: ${data.needsReview}", "empty": "Η τιμή του χαρακτηριστικού ARIA αγνοείται όταν είναι κενή: ${data.needsReview}"}}, "aria-valid-attr": {"pass": "Το όνομα χαρακτηριστικού ARIA είναι έγκυρο", "fail": {"singular": "Μη έγκυρο όνομα χαρακτηριστικού ARIA: ${data.values}", "plural": "Μη έγκυρα ονόματα χαρακτηριστικών ARIA: ${data.values}"}}, "deprecatedrole": {"pass": "Ο ρόλος ARIA δεν έχει καταργηθεί", "fail": "Ο ρόλος που χρησιμοποιείται έχει καταργηθεί: ${data}"}, "fallbackrole": {"pass": "Χρησιμοποιείται μόνο μια τιμή ρόλου", "fail": "Χρησιμοποιήστε μόνο μία τιμή ρόλου, καθώς οι εναλλακτικοί ρόλοι δεν υποστηρίζονται σε παλαιότερα προγράμματα περιήγησης", "incomplete": "Χρησιμοποιήστε μόνο τους ρόλους 'presentation' ή 'none' επειδή είναι συνώνυμοι."}, "has-global-aria-attribute": {"pass": {"singular": "Το στοιχείο έχει καθολικό χαρακτηριστικό ARIA: ${data.values}", "plural": "Το στοιχείο έχει καθολικά χαρακτηριστικά ARIA: ${data.values}"}, "fail": "Το στοιχείο δεν έχει καθολικό χαρακτηριστικό ARIA"}, "has-widget-role": {"pass": "Το στοιχείο έχει ρόλο widget.", "fail": "Το στοιχείο δεν έχει ρόλο widget."}, "invalidrole": {"pass": "Ο ρόλος ARIA είναι έγκυρος", "fail": {"singular": "Ο ρόλος πρέπει να είναι ένας από τους έγκυρους ρόλους ARIA: ${data.values}", "plural": "Οι ρόλοι πρέπει να είναι ένας από τους έγκυρους ρόλους ARIA: ${data.values}"}}, "is-element-focusable": {"pass": "Το στοιχείο μπορεί να εστιαστεί.", "fail": "Το στοιχείο δεν μπορεί να εστιαστεί."}, "no-implicit-explicit-label": {"pass": "Δεν υπάρχει αναντιστοιχία μεταξύ <label> και προσβάσιμου ονόματος", "incomplete": "Βεβαιωθείτε ότι το <label> δεν χρειάζεται να αποτελεί μέρος του ονόματος του πεδίου ARIA ${data}"}, "unsupportedrole": {"pass": "Ο ρόλος του ARIA υποστηρίζεται", "fail": "Ο ρόλος που χρησιμοποιείται δεν υποστηρίζεται ευρέως σε προγράμματα ανάγνωσης οθόνης και υποστηρικτικές τεχνολογίες: ${data}"}, "valid-scrollable-semantics": {"pass": "Το στοιχεί<PERSON> έχει έγκυρη σημασιολογία για ένα στοιχείο στη σειρά εστίασης.", "fail": "Το στοιχείο έχει μη έγκυρη σημασιολογία για ένα στοιχείο στη σειρά εστίασης."}, "color-contrast-enhanced": {"pass": "Το στοιχείο έχει επαρκώς ενισχυμένη χρωματική αντίθεση ${data.contrastRatio}", "fail": {"default": "Το στοιχείο έχει ανεπαρκώς ενισχυμένη χρωματική αντίθεση ${data.contrastRatio} (χρώμα προσκηνίου: ${data.fgColor}, χρώμα φόντου: ${data.bgColor}, μέγεθος γραμματοσειράς: ${data.fontSize}, βάρος γραμματοσειράς: ${data.fontWeight}). Αναμενόμενος λόγος αντίθεσης ${data.expectedContrastRatio}", "fgOnShadowColor": "Το στοιχείο έχει ανεπαρκώς ενισχυμένη χρωματική αντίθεση ${data.contrastRatio} μεταξύ του χρώματος του προσκηνίου και του χρώματος σκιάς (χρώμα προσκηνίου: ${data.fgColor}, χρώμα σκιάς κειμένου: ${data.shadowColor}, μέγεθος γραμματοσειράς: ${data.fontSize}, βάρος γραμματοσειράς: ${data.fontWeight}). Αναμενόμενος λόγος αντίθεσης ${data.expectedContrastRatio}", "shadowOnBgColor": "Το στοιχείο έχει ανεπαρκώς ενισχυμένη χρωματική αντίθεση ${data.contrastRatio} μεταξύ του χρώματος σκιάς και του χρώματος φόντου (χρώμα σκιάς κειμένου: ${data.shadowColor}, χρώμα φόντου: ${data.bgColor}, μέγεθος γραμματοσειράς: ${data.fontSize }, βάρος γραμματοσειράς: ${data.fontWeight}). Αναμενόμενος λόγος αντίθεσης ${data.expectedContrastRatio}"}, "incomplete": {"default": "Δεν είναι δυνατός ο προσδιορισμός της αναλογίας αντίθεσης", "bgImage": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω εικόνας φόντου", "bgGradient": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω διαβάθμισης φόντου", "imgNode": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή το στοιχείο περιέχει έναν κόμβο εικόνας", "bgOverlap": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή επικαλύπτεται από άλλο στοιχείο", "fgAlpha": "Το χρώμα του προσκηνίου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω της διαφάνειας άλφα", "elmPartiallyObscured": "Δεν ήταν δυνα<PERSON><PERSON>ς ο προσδιορισμός του χρώματος φόντου του στοιχείου επειδή είναι μερικώς καλυμμένο από άλλο στοιχείο", "elmPartiallyObscuring": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή επικαλύπτει εν μέρει άλλα στοιχεία", "outsideViewport": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή βρίσκεται εκτός του viewport", "equalRatio": "Το στοιχείο έχει αναλογ<PERSON>α αντίθεσης 1:1 με το φόντο", "shortTextContent": "Το περιεχόμενο του στοιχείου είναι πολύ σύντομο για να προσδιοριστεί εάν πρόκειται για πραγματικό περιεχόμενο κειμένου", "nonBmp": "Το περιεχόμενο του στοιχείου περιέχει μόνο χαρακτήρες που δεν είναι κειμένου", "pseudoContent": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω ψευδοστοιχείου"}}, "color-contrast": {"pass": {"default": "Το στοιχείο έχει επαρκή χρωματική αντίθεση ${data.contrastRatio}", "hidden": "Το στοιχείο είναι κρυμμένο"}, "fail": {"default": "Το στοιχείο έχει ανεπαρκή χρωματική αντίθεση ${data.contrastRatio} (χρώμα προσκηνίου: ${data.fgColor}, χρώμα φόντου: ${data.bgColor}, μέγεθος γραμματοσειράς: ${data.fontSize}, βάρος γραμματοσειράς: ${data.fontWeight}). Αναμενόμενος λόγος αντίθεσης ${data.expectedContrastRatio}", "fgOnShadowColor": "Το στοιχείο έχει ανεπαρκή χρωματική αντίθεση ${data.contrastRatio} μεταξύ του χρώματος προσκηνίου και σκιάς (χρώμα προσκηνίου: ${data.fgColor}, χρώμα σκιάς κειμένου: ${data.shadowColor}, μέγεθος γραμματοσειράς: ${data.fontSize}, βάρος γραμματοσειράς: ${data.fontWeight}). Αναμενόμενος λόγος αντίθεσης ${data.expectedContrastRatio}", "shadowOnBgColor": "Το στοιχείο έχει ανεπαρκή χρωματική αντίθεση ${data.contrastRatio} μεταξύ του χρώματος σκιάς και του χρώματος φόντου (χρώμα σκιάς κειμένου: ${data.shadowColor}, χρώμα φόντου: ${data.bgColor}, μέγεθος γραμματοσειράς: ${data.fontSize}, βάρος γραμματοσειράς: ${data.fontWeight}). Αναμενόμενος λόγος αντίθεσης ${data.expectedContrastRatio}"}, "incomplete": {"default": "Δεν είναι δυνατός ο προσδιορισμός της αναλογίας αντίθεσης", "bgImage": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω εικόνας φόντου", "bgGradient": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω διαβάθμισης φόντου", "imgNode": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή το στοιχείο περιέχει έναν κόμβο εικόνας", "bgOverlap": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή επικαλύπτεται από άλλο στοιχείο", "fgAlpha": "Το χρώμα του προσκηνίου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω διαφάνειας άλφα", "elmPartiallyObscured": "Δεν ήταν δυνα<PERSON><PERSON>ς ο προσδιορισμός του χρώματος φόντου του στοιχείου επειδή είναι μερικώς καλυμμένο από άλλο στοιχείο", "elmPartiallyObscuring": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή επικαλύπτει εν μέρει άλλα στοιχεία", "outsideViewport": "Το χρώμα φόντου του στοιχείου δεν ήταν δυνατό να προσδιοριστεί επειδή βρίσκεται εκτός viewport", "equalRatio": "Το στοιχείο έχει αναλογ<PERSON>α αντίθεσης 1:1 με το φόντο", "shortTextContent": "Το περιεχόμενο του στοιχείου είναι πολύ σύντομο για να προσδιοριστεί εάν πρόκειται για πραγματικό περιεχόμενο κειμένου", "nonBmp": "Το περιεχόμενο του στοιχείου περιέχει μόνο χαρακτήρες εκτός κειμένου", "pseudoContent": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός του χρώματος φόντου του στοιχείου λόγω ψευδοστοιχείου"}}, "link-in-text-block-style": {"pass": "Οι σύνδεσμοι μπορούν να διακριθούν από το περιβάλλον κείμενο εφαρμόζοντας οπτικό στυλ", "fail": "Ο σύνδεσμος δεν έχει στυλ (π.χ. underline) που να τον ξεχωρίζει από το περιβάλλον κείμενο"}, "link-in-text-block": {"pass": "Οι σύνδεσμοι μπορούν να διακριθούν από το περιβάλλον κείμενο με άλλο τρόπο εκτός από το χρώμα", "fail": {"fgContrast": "Ο σύνδεσμος έχει ανεπαρκή χρωματική αντίθεση ${data.contrastRatio}:1 με το περιβάλλον κείμενο. (Η ελάχιστη αντίθεση είναι ${data.requiredContrastRatio}:1, κείμενο συνδέσμου: ${data.nodeColor}, περιβάλλον κείμενο: ${data.parentColor})", "bgContrast": "Το φόντο του συνδέσμου έχει ανεπαρκή χρωματική αντίθεση ${data.contrastRatio} (η ελάχιστη αντίθεση είναι ${data.requiredContrastRatio}:1, χρώμα φόντου συνδέσμου: ${data.nodeBackgroundColor}, χρώμα φόντου περιβάλλοντος: ${data.parentBackgroundColor})"}, "incomplete": {"default": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός της αναλογίας αντίθεσης προσκηνίου του στοιχείου", "bgContrast": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός της αναλογίας αντίθεσης φόντου του στοιχείου", "bgImage": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός της αναλογίας αντίθεσης του στοιχείου λόγω εικόνας φόντου", "bgGradient": "Η αναλογία αντίθεσης του στοιχείου δεν ήταν δυνατό να προσδιοριστεί λόγω gradient φόντου", "imgNode": "Δεν ήταν δυνα<PERSON><PERSON>ς ο προσδιορισμός της αναλογίας αντίθεσης του στοιχείου επειδή το στοιχείο περιέχει έναν κόμβο εικόνας", "bgOverlap": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός της αναλογίας αντίθεσης του στοιχείου λόγω επικάλυψης στοιχείων"}}, "autocomplete-appropriate": {"pass": "η τιμή αυτόματης συμπλήρωσης βρίσκεται σε ένα κατάλληλο στοιχείο", "fail": "η τιμή αυτόματης συμπλήρωσης είναι ακατάλληλη για αυτόν τον τύπο εισόδου"}, "autocomplete-valid": {"pass": "το χαρακτη<PERSON>ιστι<PERSON><PERSON> αυτόματης συμπλήρωσης έχει μορφοποιηθεί σωστά", "fail": "το χαρακτη<PERSON>ιστι<PERSON><PERSON> αυτόματης συμπλήρωσης δεν έχει μορφοποιηθεί σωστά"}, "accesskeys": {"pass": "Η τιμή χαρακτηριστικού Accesskey είναι μοναδική", "fail": "Το έγγραφ<PERSON> έχει πολλά στοιχεία με το ίδιο κλειδί πρόσβασης"}, "focusable-content": {"pass": "Το στοιχείο περιέχει στοιχεία με δυνατότητα εστίασης", "fail": "Το στοιχείο πρέπει να έχει εστιασμένο περιεχόμενο"}, "focusable-disabled": {"pass": "Δεν περιέχονται στοιχεία με δυνατότητα εστίασης μέσα στο στοιχείο", "incomplete": "Ελέγξτε εάν τα εστιάσιμα στοιχεία μετακινούν αμέσως την ένδειξη εστίασης", "fail": "Το περιεχόμενο με δυνατότητα εστίασης θα πρέπει να απενεργοποιηθεί ή να αφαιρεθεί από το DOM"}, "focusable-element": {"pass": "Το στοιχείο είναι εστιασμένο", "fail": "Το στοιχείο πρέπει να είναι εστιασμένο"}, "focusable-modal-open": {"pass": "Δεν υπάρχουν εστιάσιμα στοιχεία ενώ ένα modal είναι ανοιχτό", "incomplete": "Βεβαιωθείτε ότι τα στοιχεία με δυνατότητα εστίασης δεν έχουν δυνατότητα tabbable στην τρέχουσα κατάσταση"}, "focusable-no-name": {"pass": "Το στοιχείο δεν είναι σε σειρά tab ή έχει προσβάσιμο κείμενο", "fail": "Το στοιχείο είναι σε σειρά tab και δεν έχει προσβάσιμο κείμενο", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν το στοιχείο έχει προσβάσιμο όνομα"}, "focusable-not-tabbable": {"pass": "Δεν περιέχονται στοιχεία με δυνατότητα εστίασης μέσα στο στοιχείο", "incomplete": "Ελέγξτε εάν τα εστιάσιμα στοιχεία μετακινούν αμέσως την ένδειξη εστίασης", "fail": "Το περιεχόμενο με δυνατότητα εστίασης θα πρέπει να έχει tabindex='-1' ή να αφαιρεθεί από το DOM"}, "frame-focusable-content": {"pass": "Το στοιχεί<PERSON> δεν έχει εστιάσιμους απογόνους", "fail": "Το στοιχεί<PERSON> έχει απογόνους με δυνατότητα εστίασης", "incomplete": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός εάν το στοιχείο έχει απογόνους"}, "landmark-is-top-level": {"pass": "Το ορόσημο ${data.role} βρίσκεται στο ανώτατο επίπεδο.", "fail": "Το ορόσημο ${data.role} περιέχεται σε άλλο ορόσημο."}, "no-focusable-content": {"pass": "Το στοιχεί<PERSON> δεν έχει εστιάσιμους απογόνους", "fail": {"default": "Το στοιχεί<PERSON> έχει απογόνους με δυνατότητα εστίασης", "notHidden": "Η χρήση  αρνητικού tabindex σε ένα στοιχείο μέσα σε ένα διαδραστικό στοιχείο ελέγχου δεν εμποδίζει τις υποστηρικτικές τεχνολογίες να εστιάσουν το στοιχείο (ακόμη και με το 'aria-hidden=true')"}, "incomplete": "Δεν ήταν δυν<PERSON><PERSON><PERSON><PERSON> ο προσδιορισμός για το εάν το στοιχείο έχει απογόνους"}, "page-has-heading-one": {"pass": "Η σελίδα έχει τουλάχιστον μία επικεφαλίδα επιπέδου 1", "fail": "Η σελίδα πρέπει να έχει επικεφαλίδα επιπέδου 1"}, "page-has-main": {"pass": "Το έγγρα<PERSON>ο έχει τουλάχιστον ένα κύριο ορόσημο", "fail": "Το έγγραφο δεν έχει κύριο ορόσημο"}, "page-no-duplicate-banner": {"pass": "Το έγγραφο δεν έχει περισσότερα από ένα ορόσημα banner", "fail": "Το έγγραφο έχει περισσότερα από ένα ορόσημα banner"}, "page-no-duplicate-contentinfo": {"pass": "Το έγγραφο δεν έχει περισσότερα από ένα ορόσημα contentinfo", "fail": "Το έγγραφο έχει περισσότερα από ένα ορόσημα contentinfo"}, "page-no-duplicate-main": {"pass": "Το έγγραφο δεν έχει περισσότερα από ένα κύρια ορόσημα", "fail": "Το έγγραφο έχει περισσότερα από ένα κύρια ορόσημα"}, "tabindex": {"pass": "Το στοιχείο δεν έχει tabindex μεγαλύτερο από 0", "fail": "Το στοιχείο έχει tabindex μεγαλύτερο από 0"}, "alt-space-value": {"pass": "Το στοιχείο έχει μια έγκυρη τιμή χαρακτηριστικού alt", "fail": "Το στοιχεί<PERSON> έχει ένα χαρακτηριστικό alt που περιέχει μόνο έναν χαρακτήρα διαστήματος, το οποίο δεν αγνοείται από όλα τα προγράμματα ανάγνωσης οθόνης"}, "duplicate-img-label": {"pass": "Το στοιχείο δεν αντιγράφει υπάρχον κείμενο στο πεδίο alt του <img>", "fail": "Το στοιχείο περιέχει στοιχείο <img> με κείμενο alt που αντιγράφει το υπάρχον κείμενο"}, "explicit-label": {"pass": "Το στοιχείο φόρμας έχει ρητό <label>", "fail": "Το στοιχείο φόρμας δέν έχει ρητό <label>", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν το στοιχείο φόρμας έχει ρητό <label>"}, "help-same-as-label": {"pass": "Το κείμενο βοήθειας (τίτλος ή aria-describedby) δεν αντιγράφει το κείμενο της ετικέτας", "fail": "Το κείμενο βοήθειας (τίτλος ή aria-describedby) είναι το ίδιο με το κείμενο της ετικέτας"}, "hidden-explicit-label": {"pass": "Το στοιχεί<PERSON> φόρμας έχει ένα ορατό και ρητό <label>", "fail": "Το στοιχεί<PERSON> φόρμας έχει ρητό <label> που είναι κρυφό", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν το στοιχείο φόρμας έχει ρητό <label> που είναι κρυφό"}, "implicit-label": {"pass": "Το στοιχεί<PERSON> φόρμας έχει ένα σιωπηρό (wrapped) <label>", "fail": "Το στοιχείο φόρμας δέν έχει ένα σιωπηρό (wrapped) <label>", "incomplete": "Unable to determine if form element has an implicit (wrapped} <label>"}, "label-content-name-mismatch": {"pass": "Το στοιχεί<PERSON> περιέχει ορατό κείμενο ως μέρος του προσβάσιμου ονόματος του", "fail": "Το κείμενο μέσα στο στοιχείο δεν περιλαμβάνεται στο προσβάσιμο όνομα"}, "multiple-label": {"pass": "Το πεδίο φόρμας δεν έχει πολλά στοιχεία ετικέτας", "incomplete": "Τα πολλαπλά στοιχεία ετικετών δεν υποστηρίζονται ευρέως στις υποστηρικτικές τεχνολογίες. Βεβαιωθείτε ότι η πρώτη ετικέτα περιέχει όλες τις απαραίτητες πληροφορίες."}, "title-only": {"pass": "Το στοιχεί<PERSON> φόρμας δεν χρησιμοποι<PERSON>ί αποκλειστικά το χαρακτηριστικό τίτλου για την ετικέτα του", "fail": "Μόνο ο τίτλος χρησιμοποιείται για τη δημιουργία ετικέτας για το στοιχείο φόρμας"}, "landmark-is-unique": {"pass": "Τα ορόσημα πρέπει να έχουν μονα<PERSON><PERSON><PERSON><PERSON> ρόλο ή συνδυασμό ρόλου/ετικέτας/τίτλου (δηλαδή προσβάσιμο όνομα).", "fail": "Το ορόσημο πρέπει να έχει ένα μοναδικό χαρακτηριστικό aria-label, aria-labelledby ή τίτλο για να διακρίνονται τα ορόσημα"}, "has-lang": {"pass": "Το στοιχείο <html> έχει ένα χαρακτηριστικό lang", "fail": {"noXHTML": "Το χαρακτηριστικό xml:lang δεν είναι έγκυρο σε σελίδες HTML, χρησιμοποιήστε το χαρακτηριστικό lang.", "noLang": "Το στοιχείο <html> δεν έχει χαρακτηριστικό lang"}}, "valid-lang": {"pass": "Η τιμή του χαρακτηριστικού lang περιλαμβάνεται στη λίστα των έγκυρων γλωσσών", "fail": "Η τιμή του χαρακτηριστικού lang δεν περιλαμβάνεται στη λίστα έγκυρων γλωσσών"}, "xml-lang-mismatch": {"pass": "Τα χαρακτηριστικ<PERSON> Lang και xml:lang έχουν την ίδια βασική γλώσσα", "fail": "Τα χαρακτηριστικ<PERSON> Lang και xml:lang δεν έχουν την ίδια βασική γλώσσα"}, "dlitem": {"pass": "Το στοιχεί<PERSON> λίστας περιγραφής έχει ένα γονικό στοιχείο <dl>", "fail": "Το στοιχεί<PERSON> λίστας περιγραφής δεν έχει γονικό στοιχείο <dl>"}, "listitem": {"pass": "Το στοιχεί<PERSON> λίστας έχει ένα γονικό στοιχείο <ul>, <ol> ή role=\"list\".", "fail": {"default": "Το στοιχεί<PERSON> λίστας δεν έχει γονικό στοιχείο <ul>, <ol>", "roleNotValid": "Το στοιχεί<PERSON> λίστας δεν έχει <ul>, <ol> γονικό στοιχείο χωρίς ρόλο ή role=\"list\""}}, "only-dlitems": {"pass": "Το στοιχείο dl έχει μόνο απευθείας θυγατρικά που επιτρέπεται να βρίσκονται μέσα σε στοιχεία <dt>, <dd> ή <div>", "fail": "Το στοιχείο dl έχει απευθείας θυγατρικά που δεν επιτρέπονται: ${data.values}"}, "only-listitems": {"pass": "Το στοιχεί<PERSON> λίστας έχει μόνο απευθείας θυγατρικά που επιτρέπονται μέσα σε στοιχεία <li>", "fail": "Το στοιχε<PERSON><PERSON> λίστας έχει απευθείας θυγατρικά που δεν επιτρέπονται: ${data.values}"}, "structured-dlitems": {"pass": "Όταν δεν είναι κεν<PERSON>, το στοιχείο έχει και τα δύο στοιχεία <dt> και <dd>", "fail": "Όταν δεν είνα<PERSON> κεν<PERSON>, το στοιχείο δεν έχει τουλάχιστον ένα στοιχείο <dt> ακολουθούμενο από τουλάχιστον ένα στοιχείο <dd>"}, "caption": {"pass": "Το στοιχεί<PERSON> πολυμέσων έχει ένα κομμάτι υπότιτλων", "incomplete": "Ελέγξτε ότι οι υπότιτλοι είναι διαθέσιμοι για το στοιχείο"}, "frame-tested": {"pass": "Το iframe δοκιμάστηκε με το ax-core", "fail": "Το iframe δεν ήταν δυνατό να δοκιμαστεί με το axe-core", "incomplete": "Το iframe πρέπει να δοκιμαστεί με το axe-core"}, "no-autoplay-audio": {"pass": "Το <video> ή το <audio> δεν παράγει ήχο για μεγαλύτερη από την επιτρεπόμενη διάρκεια ή διαθέτει μηχανισμό ελέγχου", "fail": "Το <video> ή το <audio> εξάγει ήχο για μεγαλύτερη από την επιτρεπόμενη διάρκεια και δεν διαθέτει μηχανισμό ελέγχου", "incomplete": "Βεβαιωθείτε ότι το <video> ή το <audio> δεν παράγει ήχο για μεγαλύτερη από την επιτρεπόμενη διάρκεια ή παρέχει μηχανισμό ελέγχου"}, "css-orientation-lock": {"pass": "Η οθόνη είναι λειτουργική και το κλείδωμα προσανατολισμού δεν υπάρχει", "fail": "Εφαρμόζεται κλείδωμα προσανατολισμού CSS και καθιστά την οθόνη μη λειτουργική", "incomplete": "Δεν είναι δυνατός ο προσδιορισμός ύπαρξης κλειδώματος προσανατολισμού CSS"}, "meta-viewport-large": {"pass": "Η ετικέτα <meta> δεν εμποδίζει τη σημαντική μεγέθυνση σε κινητές συσκευές", "fail": "Η ετικέτα <meta> περιορίζει τη μεγέθυνση σε κινητές συσκευές"}, "meta-viewport": {"pass": "Η ετικέτα <meta> δεν απενεργοποιεί το ζουμ σε κινητές συσκευές", "fail": "Η ετικέτα ${data} στην ετικέτα <meta> απενεργοποιεί το ζουμ σε κινητές συσκευές"}, "target-offset": {"pass": "Ο στόχος έχει επαρκή απόσταση από τους πλησιέστερους γείτονές του. Ο ασφαλής χώρος με δυνατότητα κλικ έχει διάμετρο ${data.closestOffset}px που είναι τουλάχιστον ${data.minOffset}px.", "fail": "Ο στόχος δεν έχει επαρκή απόσταση από τους πλησιέστερους γείτονές του. Ο ασφαλής χώρος με δυνατότητα κλικ έχει διάμετρο ${data.closestOffset}px αντί για τουλάχιστον ${data.minOffset}px.", "incomplete": {"default": "Το στοιχείο με αρνητικό tabindex έχει ανεπαρκή χώρο προς τους πλησιέστερους γείτονές του. Ο ασφαλής χώρος με δυνατότητα κλικ έχει διάμετρο ${data.closestOffset}px αντί για τουλάχιστον ${data.minOffset}px. Είναι αυτός ο στόχος;", "nonTabbableNeighbor": "Ο στόχος δεν έχει επαρκή χώρο προς τους πλησιέστερους γείτονές του. Ο ασφαλής χώρος με δυνατότητα κλικ έχει διάμετρο ${data.closestOffset}px αντί για τουλάχιστον ${data.minOffset}px. Είναι στόχος ο γείτονας;"}}, "target-size": {"pass": {"default": "Το στοιχείο ελέγχου έχει επαρκές μέγεθος (${data.width}px επί ${data.height}px, θα πρέπει να είναι τουλάχιστον ${data.minSize}px επί ${data.minSize}px)", "obscured": "Το στοιχείο ελέγχου αγνοείται επειδή είναι πλήρως κρυμμένο και επομένως δεν μπορεί να γίνει κλικ"}, "fail": {"default": "Ο στόχος έχει ανεπαρκές μέγεθος (${data.width}px επί ${data.height}px, θα πρέπει να είναι τουλάχιστον ${data.minSize}px επί ${data.minSize}px)", "partiallyObscured": "Το μέγεθος του στόχου είναι ανεπαρκές επειδή είναι μερικώς κρυμμένο (το μικρότερο διάστημα είναι ${data.width}px επί ${data.height}px, θα πρέπει να είναι τουλάχιστον ${data.minSize}px επί ${data.minSize}px)"}, "incomplete": {"default": "Το στοιχείο με αρνητικό tabindex έχει ανεπαρκές μέγεθος (${data.width}px επί ${data.height}px, θα πρέπει να είναι τουλάχιστον ${data.minSize}px επί ${data.minSize}px). Είναι στόχος;", "contentOverflow": "Το μέγεθος του στοιχείου δεν ήταν δυνατό να προσδιοριστεί με ακρίβεια λόγω του περιεχομένου υπερχείλισης", "partiallyObscured": "Το στοιχείο με αρνητικό tabindex δεν έχει επαρκές μέγεθος επειδή είναι μερικώς κρυμμένο (το μικρότερο διάστημα είναι ${data.width}px επί ${data.height}px, θα πρέπει να είναι τουλάχιστον ${data.minSize}px επί ${data.minSize} px). Είναι στόχος;", "partiallyObscuredNonTabbable": "Ο στόχος έχει ανεπαρκές μέγεθος επειδή είναι μερικώς καλυμμένος από έναν γείτονα με αρνητικό tabindex (το μικρότερο διάστημα είναι ${data.width}px επί ${data.height}px, θα πρέπει να είναι τουλάχιστον ${data.minSize}px επί ${data.minSize}px). Είναι στόχος ο γείτονας;"}}, "header-present": {"pass": "Η σελίδα έχει επικεφαλίδα", "fail": "Η σελίδα δεν έχει επικεφαλίδα"}, "heading-order": {"pass": "Έγκυρη σειρά επικεφαλίδας", "fail": "Μη έγκυρη σειρά επικεφαλίδας", "incomplete": "Δεν είναι δυνατός ο προσδιορισμός της προηγούμενης επικεφαλίδας"}, "identical-links-same-purpose": {"pass": "Δεν υπάρχουν άλλοι σύνδεσμοι με το ίδιο όνομα που να πηγαίνουν σε διαφορετική διεύθυνση URL", "incomplete": "Ελέγξτε ότι οι σύνδεσμοι έχουν τον ίδιο σκοπό ή είναι σκόπιμα διφορούμενοι."}, "internal-link-present": {"pass": "Βρέθηκε έγκυρο skip link", "fail": "Δεν βρέθηκε έγκυρο skip link"}, "landmark": {"pass": "Η σελίδα έχει περιοχή ορόσημο", "fail": "Η σελίδα δεν έχει περιοχή ορόσημο"}, "meta-refresh-no-exceptions": {"pass": "Η ετικέτα <meta> δεν ανανεώνει αμέσως τη σελίδα", "fail": "Η ετικέτα <meta> αναγκάζει σε ανανέωση της σελίδας μετά από κάποια χρονική στιγμή"}, "meta-refresh": {"pass": "Η ετικέτα <meta> δεν ανανεώνει αμέσως τη σελίδα", "fail": "Η ετικέτα <meta> αναγκάζει σε ανανέωση της σελίδας μετά από κάποια χρονική στιγμή (λιγότερο από 20 ώρες)"}, "p-as-heading": {"pass": "Τα στοιχεία <p> δεν έχουν στυλ επικεφαλίδων", "fail": "Στοιχεία επικεφαλίδας πρέπει να χρησιμοποιούνται αντί για στοιχεία <p> με στυλ επικεφαλίδας", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν τα στοιχεία <p> έχουν στυλ επικεφαλίδων"}, "region": {"pass": "Όλο το περιεχόμενο της σελίδας περιλαμβάνεται από ορόσημα", "fail": "Ορισμένο περιεχόμενο της σελίδας δεν περιλαμβάνεται από ορόσημα"}, "skip-link": {"pass": "Υπάρχει στόχος για skip link", "incomplete": "Ο στόχος του skip link θα πρέπει να είναι ορατός κατά την ενεργοποίηση", "fail": "Δεν υπάρχει στόχος για skip link"}, "unique-frame-title": {"pass": "Το χαρακτηριστικό title του στοιχείου είναι μοναδικό", "fail": "Το χαρακτηριστικό title του στοιχείου δεν είναι μοναδικό"}, "duplicate-id-active": {"pass": "Το έγγρα<PERSON><PERSON> δεν έχει ενε<PERSON><PERSON><PERSON> στοιχεία που μοιράζονται το ίδιο χαρακτηριστικό id", "fail": "Το έγγραφο έχει ενεργά στοιχεία με το ίδιο χαρακτηριστικό id: ${data}"}, "duplicate-id-aria": {"pass": "Το έγγραφ<PERSON> δεν έχει πολλα<PERSON><PERSON><PERSON> στοιχεία που αναφέρονται με ARIA ή ετικέτες που μοιράζονται το ίδιο χαρακτηριστικό id", "fail": "Το έγγραφο έχει πολλαπλά στοιχεία που αναφέρονται με ARIA με το ίδιο χαρακτηριστικό id: ${data}"}, "duplicate-id": {"pass": "Το έγγρα<PERSON><PERSON> δεν έχει πολλα<PERSON><PERSON><PERSON> στατικά στοιχεία που να μοιράζονται το ίδιο χαρακτηριστικό id", "fail": "Το έγγραφο έχει πολλαπλά στατικ<PERSON> στοιχεία με το ίδιο χαρακτηριστικό id: ${data}"}, "aria-label": {"pass": "Το χαρακτηριστικό aria-label υπάρχει και δεν είναι κενό", "fail": "Το χαρακτηριστικό aria-label δεν υπάρχει ή είναι κενό"}, "aria-labelledby": {"pass": "Το χαρακτηριστικό aria-labelledby υπάρχει και παραπέμπει σε στοιχεία που είναι ορατά στους αναγνώστες οθόνης", "fail": "Το χαρακτηριστικό aria-labelledby δεν υπάρχει, αναφέρεται σε στοιχεία που δεν υπάρχουν ή παραπέμπει σε στοιχεία που είναι κενά", "incomplete": "βεβαιωθείτε ότι το aria-labelledby παραπέμπει σε ένα υπάρχον στοιχείο"}, "avoid-inline-spacing": {"pass": "Δεν έχουν καθοριστεί ενσωματωμένα στυλ με '!important' που επηρεάζουν το διάστημα κειμένου", "fail": {"singular": "Καταργήστε το '!important' από το ενσωματωμένο στυλ ${data.values}, καθώς η παράκαμψη του δεν υποστηρίζεται από τα περισσότερα προγράμματα περιήγησης", "plural": "Καταργήστε το '!important' από τα ενσωματωμένα στυλ ${data.values}, καθώς η παράκαμψη τους δεν υποστηρίζεται από τα περισσότερα προγράμματα περιήγησης"}}, "button-has-visible-text": {"pass": "Το στοιχείο έχει εσωτερικό κείμενο που είναι ορατό στους αναγνώστες οθόνης", "fail": "Το στοιχεί<PERSON> δεν έχει εσωτερικ<PERSON> κείμενο που είναι ορατό στους αναγνώστες οθόνης", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν το στοιχείο έχει παιδιά"}, "doc-has-title": {"pass": "Το έγγραφο έχει μη κενό στοιχείο <title>", "fail": "Το έγγραφο δεν έχει μη κενό στοιχείο <title>"}, "exists": {"pass": "Το στοιχείο δεν υπάρχει", "incomplete": "Το στοιχείο υπάρχει"}, "has-alt": {"pass": "Το στοιχείο έχει χαρακτηριστικό alt", "fail": "Το στοιχείο δεν έχει χαρακτηριστικό alt"}, "has-visible-text": {"pass": "Το στοιχεί<PERSON> έχει κείμενο που είναι ορατό στους αναγνώστες οθόνης", "fail": "Το στοιχεί<PERSON> δεν έχει κείμενο που είναι ορατό στους αναγνώστες οθόνης", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν το στοιχείο έχει παιδιά"}, "important-letter-spacing": {"pass": "Το letter-spacing στο χαρακτηριστικό style δεν έχει οριστεί σε !important ή πληροί το ελάχιστο", "fail": "Το letter-spacing  στο χαρακτηριστικό style δεν πρέπει να χρησιμοποιεί !important ή να είναι στο ${data.minValue}em (τρέχον ${data.value}em)"}, "important-line-height": {"pass": "Το line-height στο χαρακτηριστικό style δεν έχει οριστεί σε !important ή πληροί το ελάχιστο", "fail": "Το line-height στο χαρακτηριστικό style δεν πρέπει να χρησιμοποιεί !important ή να είναι στο ${data.minValue}em (τρέχον ${data.value}em)"}, "important-word-spacing": {"pass": "Το word-spacing στο χαρακτηριστικό style δεν έχει οριστεί σε !important ή πληροί το ελάχιστο", "fail": "Το word-spacing στο χαρακτηριστικό style δεν πρέπει να χρησιμοποιεί !important ή να είναι στο ${data.minValue}em (τρέχον ${data.value}em)"}, "is-on-screen": {"pass": "Το στοιχείο δεν είναι ορατό", "fail": "Το στοιχείο είναι ορατό"}, "non-empty-alt": {"pass": "Το στοιχείο έχει μη κενό χαρακτηριστικό alt", "fail": {"noAttr": "Το στοιχείο δεν έχει χαρακτηριστικό alt", "emptyAttr": "Το στοιχείο έχει κενό χαρακτηριστικό alt"}}, "non-empty-if-present": {"pass": {"default": "Το στοιχείο δεν έχει χαρακτηριστικό value", "has-label": "Το στοιχεί<PERSON> έχει ένα χαρακτηριστικό value με μη κενή τιμή"}, "fail": "Το στοιχείο έχει χαρακτηριστικό value αλλά είναι κενό"}, "non-empty-placeholder": {"pass": "Το στοιχείο έχει χαρακτηριστικό placeholder", "fail": {"noAttr": "Το στοιχείο δεν έχει χαρακτηριστικό placeholder", "emptyAttr": "Το στοιχεί<PERSON> έχει κενό χαρακτηριστικό placeholder"}}, "non-empty-title": {"pass": "Το στοιχείο έχει χαρακτηριστικό τίτλου", "fail": {"noAttr": "Το στοιχεί<PERSON> δεν έχει χαρακτηριστικό τίτλου", "emptyAttr": "Το στοιχεί<PERSON> έχει κενό χαρακτηριστικό τίτλου"}}, "non-empty-value": {"pass": "Το στοιχεί<PERSON> έχει ένα χαρακτηριστικό με μη κενή τιμή", "fail": {"noAttr": "Το στοιχείο δεν έχει χαρακτηριστικό τιμής", "emptyAttr": "Το στοιχεί<PERSON> έχει ένα χαρακτηριστικό με κενή τιμή"}}, "presentational-role": {"pass": "Η προεπιλεγμένη σημασιολογία του στοιχείου αντικαταστάθηκε με το role=\"${data.role}\"", "fail": {"default": "Η προεπιλεγμένη σημασιολογία του στοιχείου δεν αντικαταστάθηκε με role=\"none\" ή role=\"presentation\"", "globalAria": "Ο ρόλος του στοιχείου δεν είναι για παρουσίαση επειδή έχει ένα καθολικό χαρακτηριστικό ARIA", "focusable": "Ο ρόλος του στοιχείου δεν είναι για παρουσίαση επειδή είναι εστιασμένο", "both": "Ο ρόλος του στοιχείου δεν είναι για παρουσίαση, επειδή έχει ένα καθολικό χαρακτηριστικό ARIA και είναι εστιασμένο", "iframe": "Η χρήση του χαρακτηριστικού \"title\" σε ένα στοιχείο ${data.nodeName} με ρόλο παρουσίασης συμπεριφέρεται με ασυνέπεια μεταξύ των προγραμμάτων ανάγνωσης οθόνης"}}, "role-none": {"pass": "Η προεπιλεγμένη σημασιολογία του στοιχείου αντικαταστάθηκε με role=\"none\"", "fail": "Η προεπιλεγμένη σημασιολογία του στοιχείου δεν αντικαταστάθηκε με role=\"none\""}, "role-presentation": {"pass": "Η προεπιλεγμένη σημασιολογία του στοιχείου αντικαταστάθηκε με role=\"presentation\"", "fail": "Η προεπιλεγμένη σημασιολογία του στοιχείου δεν αντικαταστάθηκε με role=\"presentation\""}, "svg-non-empty-title": {"pass": "Το στοιχεί<PERSON> έχει θυγατρικό που είναι τίτλος", "fail": {"noTitle": "Το στοιχεί<PERSON> δεν έχει θυγατρικό που να είναι τίτλος", "emptyTitle": "Ο τίτλος του θυγατρικού στοιχείου είναι κενός"}, "incomplete": "Δεν είναι δυνατό να προσδιοριστεί αν το στοιχείο έχει ένα θυγατρικό που είναι τίτλος"}, "caption-faked": {"pass": "Η πρώτη γραμμή του πίνακα δεν χρησιμοποιείται ως λεζάντα", "fail": "Το πρώτο θυγατρικό του πίνακα θα πρέπει να είναι λεζάντα αντί για κελί πίνακα"}, "html5-scope": {"pass": "Το χαρακτηριστι<PERSON><PERSON> χρησιμοποιείται μόνο σε στοιχεία κεφαλίδας πίνακα (<th>)", "fail": "Στην HTML 5, τα χαρακτηριστικά scope μπορούν να χρησιμοποιούνται μόνο σε στοιχεία κεφαλίδας πίνακα (<th>)"}, "same-caption-summary": {"pass": "Το περιεχόμενο του χαρακτηριστικού summary και του <caption> δεν είναι διπλότυπα", "fail": "Το περιεχόμενο του χαρακτηριστικού summary και το στοιχείο <caption> είναι πανομοιότυπα", "incomplete": "Δεν είναι δυνατό να προσδιοριστεί εάν το στοιχείο <table> έχει λεζάντα"}, "scope-value": {"pass": "Το χαρακτηριστι<PERSON><PERSON>ope χρησιμοποιείται σωστά", "fail": "Η τιμή του χαρακτηριστικού scope μπορεί να είναι μόνο 'row' ή 'col'"}, "td-has-header": {"pass": "Όλα τα μη κενά κελιά δεδομένων διαθέτουν κεφαλίδες πίνακα", "fail": "Ορισμένα μη κενά κελιά δεδομένων δεν διαθέτουν κεφαλίδες πίνακα"}, "td-headers-attr": {"pass": "Το χαρακτηριστικό headers χρησιμοποιείται αποκλειστικά για αναφορά σε άλλα κελιά του πίνακα", "incomplete": "Το χαρακτηριστικό headers είναι κενό", "fail": "Το χαρακτηριστικό headers δεν χρησιμοποιείται αποκλειστικά για αναφορά σε άλλα κελιά του πίνακα"}, "th-has-data-cells": {"pass": "Όλα τα κελιά της κεφαλίδας του πίνακα αναφέρονται σε κελιά δεδομένων", "fail": "Δεν αναφέροντ<PERSON>ι όλα τα κελιά της κεφαλίδας του πίνακα σε κελιά δεδομένων", "incomplete": "Τα κελιά δεδομένων του πίνακα λείπουν ή είναι άδεια"}, "hidden-content": {"pass": "Όλο το περιεχόμενο της σελίδας έχει αναλυθεί.", "fail": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν προβλήματα κατά την ανάλυση του περιεχομένου σε αυτήν τη σελίδα.", "incomplete": "Υπάρχει κρυφό περιεχόμενο στη σελίδα που δεν αναλύθηκε. Για να το αναλύσετε, θα πρέπει να ενεργοποιήσετε την εμφάνιση αυτού του περιεχομένου."}}, "failureSummaries": {"any": {"failureMessage": "Διορθώστε οποιοδήποτε από τα παρακάτω:{{~it:value}}\n  {{=value.split('\\n').join('\\n  ')}}{{~}}"}, "none": {"failureMessage": "Διορθώστε όλα τα παρακάτω:{{~it:value}}\n  {{=value.split('\\n').join('\\n  ')}}{{~}}"}}, "incompleteFallbackMessage": "Το axe δεν μπορεί να πει τον λόγο. Ώρα για τον επιθεωρητή στοιχείων!"}