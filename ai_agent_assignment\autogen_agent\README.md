# AutoGen Code Review Team Agent

## Overview
This agent uses AutoGen to create a multi-agent code review system where different AI agents with specialized roles collaborate to provide comprehensive code analysis.

## Features
- **Multi-Agent Collaboration**: Different agents with specialized expertise
- **Role-Based Analysis**: Each agent focuses on their domain of expertise
- **Interactive Conversations**: Agents discuss and build upon each other's findings
- **Comprehensive Reviews**: Architecture, security, performance, and documentation analysis
- **Consensus Building**: Agents work together to reach final recommendations

## Agent Roles

### 1. Senior Developer Agent 🧑‍💻
- **Expertise**: Code architecture, design patterns, best practices
- **Focus**: Overall code structure, maintainability, scalability
- **Responsibilities**: Review architecture decisions, suggest improvements

### 2. Security Expert Agent 🔒
- **Expertise**: Security vulnerabilities, secure coding practices
- **Focus**: Security flaws, potential exploits, data protection
- **Responsibilities**: Identify security risks, recommend security measures

### 3. Performance Analyst Agent ⚡
- **Expertise**: Code optimization, performance bottlenecks
- **Focus**: Efficiency, resource usage, scalability issues
- **Responsibilities**: Analyze performance, suggest optimizations

### 4. Documentation Agent 📝
- **Expertise**: Code documentation, readability, maintainability
- **Focus**: Comments, docstrings, code clarity
- **Responsibilities**: Assess documentation quality, suggest improvements

### 5. Team Lead Agent 👨‍💼
- **Expertise**: Project management, coordination, final decisions
- **Focus**: Coordinating review process, making final recommendations
- **Responsibilities**: Facilitate discussion, synthesize feedback, provide final verdict

## Architecture
```
Code Review Team
├── User Input (Code to review)
├── Senior Developer Agent
├── Security Expert Agent  
├── Performance Analyst Agent
├── Documentation Agent
├── Team Lead Agent (Coordinator)
└── Final Review Report
```

## Conversation Flow
1. **Code Submission**: User submits code for review
2. **Initial Analysis**: Each specialist agent analyzes the code
3. **Discussion Phase**: Agents discuss findings and concerns
4. **Consensus Building**: Team Lead facilitates agreement on issues
5. **Final Report**: Comprehensive review with prioritized recommendations

## Usage

### Basic Usage
```python
from code_review_team import CodeReviewTeam

# Initialize the team
review_team = CodeReviewTeam()

# Submit code for review
code_to_review = """
def process_user_data(user_input):
    # Your code here
    pass
"""

# Get comprehensive review
review_result = review_team.review_code(code_to_review)
print(review_result)
```

### Streamlit Interface
```bash
cd autogen_agent
streamlit run app.py
```

## Sample Code Examples
The `sample_code/` directory contains example code files for testing:
- Python functions with various issues
- JavaScript code snippets
- Security-vulnerable code examples
- Performance-problematic code

## Demo Scenarios
1. **Security Vulnerability Review**
2. **Performance Optimization Analysis**
3. **Architecture Design Review**
4. **Documentation Quality Assessment**
5. **Multi-Issue Code Analysis**

## Files
- `code_review_team.py`: Main AutoGen implementation
- `agents.py`: Individual agent definitions
- `app.py`: Streamlit web interface
- `config.py`: Configuration settings
- `sample_code/`: Example code for testing
- `tests/`: Unit tests
