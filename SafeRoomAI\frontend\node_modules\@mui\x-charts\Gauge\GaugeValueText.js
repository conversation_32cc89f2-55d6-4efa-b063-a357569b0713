'use client';

import _extends from "@babel/runtime/helpers/esm/extends";
import _objectWithoutPropertiesLoose from "@babel/runtime/helpers/esm/objectWithoutPropertiesLoose";
const _excluded = ["text", "className"];
import * as React from 'react';
import PropTypes from 'prop-types';
import { useGaugeState } from "./GaugeProvider.js";
import { ChartsText } from "../ChartsText/index.js";
import { jsx as _jsx } from "react/jsx-runtime";
function defaultFormatter({
  value
}) {
  return value === null ? null : value.toLocaleString();
}
function GaugeValueText(props) {
  const {
      text = defaultFormatter,
      className
    } = props,
    other = _objectWithoutPropertiesLoose(props, _excluded);
  const {
    value,
    valueMin,
    valueMax,
    cx,
    cy
  } = useGaugeState();
  const formattedText = typeof text === 'function' ? text({
    value,
    valueMin,
    valueMax
  }) : text;
  if (formattedText === null) {
    return null;
  }
  return /*#__PURE__*/_jsx("g", {
    className: className,
    children: /*#__PURE__*/_jsx(ChartsText, _extends({
      x: cx,
      y: cy,
      text: formattedText,
      style: {
        textAnchor: 'middle',
        dominantBaseline: 'central'
      }
    }, other))
  });
}
process.env.NODE_ENV !== "production" ? GaugeValueText.propTypes = {
  // ----------------------------- Warning --------------------------------
  // | These PropTypes are generated from the TypeScript type definitions |
  // | To update them edit the TypeScript types and run "pnpm proptypes"  |
  // ----------------------------------------------------------------------
  /**
   * Height of a text line (in `em`).
   */
  lineHeight: PropTypes.number,
  /**
   * If `true`, the line width is computed.
   * @default false
   */
  needsComputation: PropTypes.bool,
  ownerState: PropTypes.any,
  /**
   * Style applied to text elements.
   */
  style: PropTypes.object,
  text: PropTypes.oneOfType([PropTypes.func, PropTypes.string])
} : void 0;
export { GaugeValueText };