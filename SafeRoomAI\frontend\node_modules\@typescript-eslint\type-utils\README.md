# `@typescript-eslint/type-utils`

> Type utilities for working with TypeScript within ESLint rules.

The utilities in this package are separated from `@typescript-eslint/utils` so that that package does not require a dependency on `typescript`.

## ✋ Internal Package

This is an _internal package_ to the [typescript-eslint monorepo](https://github.com/typescript-eslint/typescript-eslint).
You likely don't want to use it directly.

👉 See **https://typescript-eslint.io** for docs on typescript-eslint.
