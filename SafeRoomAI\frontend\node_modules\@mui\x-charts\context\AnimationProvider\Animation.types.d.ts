export type AnimationProviderProps = {
    /**
     * If `true`, animations are skipped.
     * If unset or `false`, the animations respects the user's `prefers-reduced-motion` setting.
     */
    skipAnimation?: boolean;
    children: React.ReactNode;
};
export type AnimationState = {
    /**
     * If `true`, animations are skipped.
     * @default undefined
     */
    skipAnimation?: boolean;
};
