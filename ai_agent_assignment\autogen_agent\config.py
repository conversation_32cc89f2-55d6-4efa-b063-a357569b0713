"""
Configuration settings for the AutoGen Code Review Team Agent
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class AutoGenConfig:
    """Configuration class for the AutoGen Code Review Team"""
    
    # API Keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Model Settings
    LLM_MODEL = "gpt-3.5-turbo"
    TEMPERATURE = 0.3  # Slightly higher for more creative discussions
    MAX_TOKENS = 1500
    
    # AutoGen Settings
    MAX_CONSECUTIVE_AUTO_REPLY = 3
    HUMAN_INPUT_MODE = "NEVER"  # For automated reviews
    CODE_EXECUTION_CONFIG = False  # Disable code execution for security
    
    # Agent Configurations
    AGENTS_CONFIG = {
        "senior_developer": {
            "name": "Senior_Developer",
            "system_message": """You are a Senior Software Developer with 10+ years of experience.
            Your expertise includes:
            - Software architecture and design patterns
            - Code structure and organization
            - Best practices and coding standards
            - Maintainability and scalability
            - SOLID principles and clean code
            
            When reviewing code, focus on:
            1. Overall architecture and design
            2. Code organization and structure
            3. Design pattern usage
            4. Maintainability concerns
            5. Scalability considerations
            
            Be constructive and provide specific suggestions for improvement."""
        },
        
        "security_expert": {
            "name": "Security_Expert", 
            "system_message": """You are a Cybersecurity Expert specializing in secure coding practices.
            Your expertise includes:
            - Security vulnerabilities (OWASP Top 10)
            - Secure coding practices
            - Data protection and privacy
            - Authentication and authorization
            - Input validation and sanitization
            
            When reviewing code, focus on:
            1. Security vulnerabilities and exploits
            2. Input validation and sanitization
            3. Authentication and authorization flaws
            4. Data exposure risks
            5. Injection attacks (SQL, XSS, etc.)
            
            Identify specific security risks and provide remediation steps."""
        },
        
        "performance_analyst": {
            "name": "Performance_Analyst",
            "system_message": """You are a Performance Optimization Specialist.
            Your expertise includes:
            - Algorithm complexity analysis
            - Memory usage optimization
            - Database query optimization
            - Caching strategies
            - Scalability bottlenecks
            
            When reviewing code, focus on:
            1. Time and space complexity
            2. Memory usage and leaks
            3. Database query efficiency
            4. Caching opportunities
            5. Scalability bottlenecks
            
            Provide specific optimization recommendations with performance impact estimates."""
        },
        
        "documentation_agent": {
            "name": "Documentation_Specialist",
            "system_message": """You are a Technical Documentation Specialist.
            Your expertise includes:
            - Code readability and clarity
            - Documentation standards
            - Comment quality and usefulness
            - API documentation
            - Code maintainability
            
            When reviewing code, focus on:
            1. Code readability and clarity
            2. Comment quality and usefulness
            3. Function/method documentation
            4. Variable and function naming
            5. Overall code maintainability
            
            Suggest improvements for code clarity and documentation."""
        },
        
        "team_lead": {
            "name": "Team_Lead",
            "system_message": """You are a Technical Team Lead responsible for coordinating code reviews.
            Your role includes:
            - Facilitating team discussions
            - Synthesizing feedback from specialists
            - Making final recommendations
            - Prioritizing issues by severity
            - Ensuring comprehensive coverage
            
            Your responsibilities:
            1. Coordinate the review process
            2. Synthesize feedback from all team members
            3. Prioritize issues by severity and impact
            4. Make final recommendations
            5. Ensure all aspects are covered
            
            Provide a final summary with prioritized action items."""
        }
    }
    
    # Review Process Configuration
    REVIEW_PROCESS = {
        "initial_analysis_rounds": 2,
        "discussion_rounds": 3,
        "consensus_rounds": 2,
        "max_total_rounds": 10
    }
    
    # Code Analysis Prompts
    INITIAL_REVIEW_PROMPT = """
    Please review the following code from your area of expertise:

    ```{language}
    {code}
    ```

    Provide a detailed analysis focusing on your specialization. Include:
    1. Key issues identified
    2. Severity assessment (Critical/High/Medium/Low)
    3. Specific recommendations
    4. Code examples for fixes (if applicable)
    """
    
    DISCUSSION_PROMPT = """
    Based on the initial reviews from the team, please discuss:
    1. Do you agree with the issues raised by other team members?
    2. Are there any additional concerns in your area of expertise?
    3. What are the priorities for fixing the identified issues?
    4. Any suggestions for the implementation approach?
    """
    
    FINAL_SUMMARY_PROMPT = """
    As the Team Lead, provide a comprehensive final review summary including:
    1. Executive Summary
    2. Critical Issues (must fix)
    3. High Priority Issues (should fix)
    4. Medium/Low Priority Issues (nice to fix)
    5. Recommended Implementation Order
    6. Overall Code Quality Assessment (1-10 scale)
    7. Estimated Effort for Fixes
    """

# Validation
if not AutoGenConfig.OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable is required")

# Create necessary directories
os.makedirs("./sample_code", exist_ok=True)
os.makedirs("./review_reports", exist_ok=True)
