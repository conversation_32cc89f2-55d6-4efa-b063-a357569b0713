# MUI System

MUI System is a set of CSS utilities to help you build custom designs more efficiently. It makes it possible to rapidly lay out custom designs.

## Installation

Install the package in your project directory with:

<!-- #npm-tag-reference -->

```bash
npm install @mui/system@^6.0.0 @emotion/react @emotion/styled
```

If you wish to use the latest version, remove the `@^6.0.0` suffix.

## Documentation

<!-- #host-reference -->

Visit [https://v6.mui.com/system/getting-started/](https://v6.mui.com/system/getting-started/) to view the full documentation.
