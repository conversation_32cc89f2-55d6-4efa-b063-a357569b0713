'use strict';
var parent = require('../../stable/typed-array/methods');
require('../../modules/esnext.uint8-array.from-base64');
require('../../modules/esnext.uint8-array.from-hex');
require('../../modules/esnext.uint8-array.set-from-base64');
require('../../modules/esnext.uint8-array.set-from-hex');
require('../../modules/esnext.uint8-array.to-base64');
require('../../modules/esnext.uint8-array.to-hex');
// TODO: Remove from `core-js@4`
require('../../modules/esnext.typed-array.find-last');
require('../../modules/esnext.typed-array.find-last-index');
require('../../modules/esnext.typed-array.to-reversed');
require('../../modules/esnext.typed-array.to-sorted');
require('../../modules/esnext.typed-array.to-spliced');
require('../../modules/esnext.typed-array.with');

module.exports = parent;
