{"version": 3, "file": "PatternVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/PatternVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAG1D,+CAA4C;AAY5C,MAAM,cAAe,SAAQ,yBAAW;IAC/B,MAAM,CAAC,SAAS,CACrB,IAAmB;QAQnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAE3B,OAAO,CACL,QAAQ,KAAK,sBAAc,CAAC,UAAU;YACtC,QAAQ,KAAK,sBAAc,CAAC,aAAa;YACzC,QAAQ,KAAK,sBAAc,CAAC,YAAY;YACxC,QAAQ,KAAK,sBAAc,CAAC,aAAa;YACzC,QAAQ,KAAK,sBAAc,CAAC,WAAW;YACvC,QAAQ,KAAK,sBAAc,CAAC,iBAAiB,CAC9C,CAAC;IACJ,CAAC;IAWD,YACE,OAA8B,EAC9B,WAA0B,EAC1B,QAAgC;QAEhC,KAAK,CAAC,OAAO,CAAC,CAAC;QAdR,8CAA4B;QAC5B,2CAAkC;QAClC,sCAGH,EAAE,EAAC;QACO,mBAAc,GAAoB,EAAE,CAAC;QAC5C,uCAAwC,EAAE,EAAC;QAQlD,uBAAA,IAAI,+BAAgB,WAAW,MAAA,CAAC;QAChC,uBAAA,IAAI,4BAAa,QAAQ,MAAA,CAAC;IAC5B,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1C,CAAC;IAES,YAAY,CAAC,OAA8B;QACnD,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACrB;IACH,CAAC;IAES,oBAAoB,CAAC,IAAmC;QAChE,uBAAA,IAAI,mCAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrC,uBAAA,IAAI,mCAAa,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IAES,iBAAiB,CAAC,OAAmC;QAC7D,uBAAA,IAAI,mCAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACxC,uBAAA,IAAI,mCAAa,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IAES,cAAc,CAAC,IAA6B;QACpD,kCAAkC;QAClC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAES,SAAS;QACjB,sDAAsD;IACxD,CAAC;IAES,UAAU,CAAC,OAA4B;;QAC/C,MAAM,eAAe,GACnB,MAAA,uBAAA,IAAI,oCAAc,CAAC,uBAAA,IAAI,oCAAc,CAAC,MAAM,GAAG,CAAC,CAAC,mCAAI,IAAI,CAAC;QAE5D,uBAAA,IAAI,gCAAU,MAAd,IAAI,EAAW,OAAO,EAAE;YACtB,QAAQ,EAAE,OAAO,KAAK,uBAAA,IAAI,mCAAa;YACvC,IAAI,EAAE,eAAe,IAAI,IAAI,IAAI,eAAe,CAAC,QAAQ,KAAK,OAAO;YACrE,WAAW,EAAE,uBAAA,IAAI,mCAAa;SAC/B,CAAC,CAAC;IACL,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,gDAAgD;QAChD,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACzC;QAED,kDAAkD;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;IAES,QAAQ,CAAC,QAA2B;QAC5C,gDAAgD;QAChD,IAAI,QAAQ,CAAC,QAAQ,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SACxC;QAED,mDAAmD;QACnD,mHAAmH;QACnH,kEAAkE;QAClE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;IAES,WAAW,CAAC,OAA6B;QACjD,uBAAA,IAAI,oCAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC7B,uBAAA,IAAI,oCAAc,CAAC,GAAG,EAAE,CAAC;IAC3B,CAAC;IAES,aAAa,CAAC,IAA4B;QAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAES,gBAAgB;QACxB,+BAA+B;IACjC,CAAC;CACF;AAEQ,wCAAc"}