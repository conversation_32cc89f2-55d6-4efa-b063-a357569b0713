"""
Streamlit Web Interface for Research Paper Analyzer Agent
"""

import streamlit as st
import os
import tempfile
from pathlib import Path
import json

# Local imports
from research_analyzer import ResearchPaperAnalyzer
from utils import FileManager

# Page configuration
st.set_page_config(
    page_title="Research Paper Analyzer",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Initialize session state
if 'analyzer' not in st.session_state:
    st.session_state.analyzer = ResearchPaperAnalyzer()
if 'paper_loaded' not in st.session_state:
    st.session_state.paper_loaded = False
if 'analysis_results' not in st.session_state:
    st.session_state.analysis_results = {}

def main():
    """Main Streamlit application"""
    
    # Title and description
    st.title("🔬 Research Paper Analyzer Agent")
    st.markdown("**LangChain-powered AI agent for academic research analysis**")
    
    # Sidebar for file upload and controls
    with st.sidebar:
        st.header("📁 Paper Upload")
        
        # File upload
        uploaded_file = st.file_uploader(
            "Choose a PDF research paper",
            type=['pdf'],
            help="Upload an academic research paper in PDF format"
        )
        
        # Load paper button
        if uploaded_file is not None:
            if st.button("🔄 Load Paper", type="primary"):
                with st.spinner("Loading and processing paper..."):
                    # Save uploaded file temporarily
                    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        tmp_path = tmp_file.name
                    
                    # Load paper using analyzer
                    success = st.session_state.analyzer.load_paper(tmp_path)
                    
                    if success:
                        st.session_state.paper_loaded = True
                        st.success("✅ Paper loaded successfully!")
                        
                        # Get paper info
                        paper_info = st.session_state.analyzer.get_paper_info()
                        st.session_state.current_paper_info = paper_info
                        
                    else:
                        st.error("❌ Failed to load paper. Please check the file.")
                    
                    # Clean up temporary file
                    os.unlink(tmp_path)
        
        # Paper information
        if st.session_state.paper_loaded:
            st.header("📊 Paper Info")
            info = st.session_state.get('current_paper_info', {})
            
            if 'metadata' in info:
                metadata = info['metadata']
                st.write(f"**Title:** {metadata.get('title', 'Unknown')}")
                st.write(f"**Author:** {metadata.get('author', 'Unknown')}")
                st.write(f"**Pages:** {metadata.get('pages', 'Unknown')}")
            
            if 'processing_info' in info:
                proc_info = info['processing_info']
                st.write(f"**Chunks:** {proc_info.get('chunks_created', 0)}")
    
    # Main content area
    if not st.session_state.paper_loaded:
        # Welcome screen
        st.markdown("""
        ## Welcome to the Research Paper Analyzer! 👋
        
        This AI agent uses **LangChain** to analyze academic research papers and provide intelligent insights.
        
        ### Features:
        - 📄 **PDF Processing**: Upload and analyze research papers
        - 🤖 **Question Answering**: Ask questions about the research content
        - 📝 **Summary Generation**: Get comprehensive paper summaries
        - 🔍 **Research Gap Analysis**: Identify future research opportunities
        - 📚 **Citation Analysis**: Analyze citation patterns and trends
        
        ### How to use:
        1. Upload a PDF research paper using the sidebar
        2. Click "Load Paper" to process the document
        3. Use the tabs below to explore different analysis features
        
        **Get started by uploading a research paper!** ⬅️
        """)
    
    else:
        # Analysis interface
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "💬 Ask Questions", 
            "📝 Summary", 
            "🔍 Research Gaps", 
            "📚 Citations", 
            "📊 Paper Info"
        ])
        
        with tab1:
            st.header("💬 Ask Questions About the Paper")
            
            # Question input
            question = st.text_input(
                "Enter your question:",
                placeholder="What are the main findings of this research?",
                help="Ask any question about the research paper content"
            )
            
            # Predefined questions
            st.subheader("🎯 Quick Questions")
            col1, col2 = st.columns(2)
            
            with col1:
                if st.button("What is the research methodology?"):
                    question = "What is the research methodology used in this paper?"
                if st.button("What are the main findings?"):
                    question = "What are the main findings and results of this research?"
            
            with col2:
                if st.button("What are the limitations?"):
                    question = "What are the limitations mentioned in this research?"
                if st.button("What is the conclusion?"):
                    question = "What is the main conclusion of this research paper?"
            
            # Process question
            if question and st.button("🔍 Get Answer", type="primary"):
                with st.spinner("Analyzing paper and generating answer..."):
                    response = st.session_state.analyzer.ask(question)
                    
                    # Display answer
                    st.subheader("📋 Answer")
                    st.write(response['answer'])
                    
                    # Display confidence and sources
                    col1, col2 = st.columns([1, 2])
                    with col1:
                        confidence_color = {
                            'high': '🟢',
                            'medium': '🟡', 
                            'low': '🔴'
                        }
                        st.write(f"**Confidence:** {confidence_color.get(response['confidence'], '⚪')} {response['confidence'].title()}")
                    
                    with col2:
                        st.write(f"**Sources:** {len(response['sources'])} relevant sections found")
                    
                    # Show sources
                    if response['sources']:
                        with st.expander("📖 View Sources"):
                            for i, source in enumerate(response['sources']):
                                st.write(f"**Source {i+1}:**")
                                st.write(source['content'])
                                st.write("---")
        
        with tab2:
            st.header("📝 Research Paper Summary")
            
            if st.button("📄 Generate Summary", type="primary"):
                with st.spinner("Generating comprehensive summary..."):
                    summary_result = st.session_state.analyzer.generate_summary()
                    st.session_state.analysis_results['summary'] = summary_result
            
            # Display summary if available
            if 'summary' in st.session_state.analysis_results:
                result = st.session_state.analysis_results['summary']
                
                if 'error' not in result:
                    st.subheader("📋 Summary")
                    st.write(result['summary'])
                    
                    # Statistics
                    col1, col2, col3 = st.columns(3)
                    stats = result.get('statistics', {})
                    
                    with col1:
                        st.metric("Total Chunks", stats.get('total_chunks', 'N/A'))
                    with col2:
                        st.metric("Token Count", stats.get('token_count', 'N/A'))
                    with col3:
                        st.metric("Citations Found", stats.get('citations_found', 'N/A'))
                    
                    # Keywords
                    if 'keywords' in result:
                        st.subheader("🔑 Key Terms")
                        keywords = result['keywords']
                        st.write(", ".join(keywords))
                
                else:
                    st.error(f"Error: {result['error']}")
        
        with tab3:
            st.header("🔍 Research Gap Analysis")
            
            if st.button("🎯 Identify Research Gaps", type="primary"):
                with st.spinner("Analyzing research gaps and opportunities..."):
                    gaps_result = st.session_state.analyzer.identify_research_gaps()
                    st.session_state.analysis_results['gaps'] = gaps_result
            
            # Display research gaps if available
            if 'gaps' in st.session_state.analysis_results:
                result = st.session_state.analysis_results['gaps']
                
                if 'error' not in result:
                    st.subheader("🎯 Research Gaps and Opportunities")
                    st.write(result['research_gaps'])
                    
                    st.info(f"📊 {result.get('analysis_scope', 'Analysis completed')}")
                
                else:
                    st.error(f"Error: {result['error']}")
        
        with tab4:
            st.header("📚 Citation Analysis")
            
            if st.button("📖 Analyze Citations", type="primary"):
                with st.spinner("Analyzing citation patterns..."):
                    citation_result = st.session_state.analyzer.analyze_citations()
                    st.session_state.analysis_results['citations'] = citation_result
            
            # Display citation analysis if available
            if 'citations' in st.session_state.analysis_results:
                result = st.session_state.analysis_results['citations']
                
                if 'error' not in result:
                    st.subheader("📊 Citation Analysis")
                    st.write(result['citation_analysis'])
                    
                    # Citation statistics
                    st.metric("Total Citations Found", result.get('total_citations', 0))
                    
                    # Show sample citations
                    if 'citations_extracted' in result and result['citations_extracted']:
                        with st.expander("📋 Sample Citations"):
                            for citation in result['citations_extracted'][:10]:
                                st.write(f"• {citation}")
                
                else:
                    st.error(f"Error: {result['error']}")
        
        with tab5:
            st.header("📊 Paper Information")
            
            if st.session_state.paper_loaded:
                info = st.session_state.get('current_paper_info', {})
                
                # Metadata
                if 'metadata' in info:
                    st.subheader("📄 Document Metadata")
                    metadata = info['metadata']
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**Title:** {metadata.get('title', 'Unknown')}")
                        st.write(f"**Author:** {metadata.get('author', 'Unknown')}")
                        st.write(f"**Subject:** {metadata.get('subject', 'Unknown')}")
                    
                    with col2:
                        st.write(f"**Pages:** {metadata.get('pages', 'Unknown')}")
                        st.write(f"**Creator:** {metadata.get('creator', 'Unknown')}")
                        st.write(f"**Creation Date:** {metadata.get('creation_date', 'Unknown')}")
                
                # Processing info
                if 'processing_info' in info:
                    st.subheader("⚙️ Processing Information")
                    proc_info = info['processing_info']
                    
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Document Chunks", proc_info.get('chunks_created', 0))
                    with col2:
                        status = "✅ Ready" if proc_info.get('vector_store_ready') else "❌ Not Ready"
                        st.write(f"**Vector Store:** {status}")
                    with col3:
                        status = "✅ Ready" if proc_info.get('qa_chain_ready') else "❌ Not Ready"
                        st.write(f"**QA Chain:** {status}")
                
                # File stats
                if 'file_stats' in info:
                    st.subheader("📁 File Statistics")
                    file_stats = info['file_stats']
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**File Size:** {file_stats.get('size', 'Unknown')}")
                    with col2:
                        status = "✅ Valid" if file_stats.get('valid_pdf') else "❌ Invalid"
                        st.write(f"**PDF Status:** {status}")

if __name__ == "__main__":
    main()
