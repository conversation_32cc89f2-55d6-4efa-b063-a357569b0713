"use strict";
'use client';

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
/**
 * @ignore - internal component.
 */
const ToggleButtonGroupButtonContext = /*#__PURE__*/React.createContext(undefined);
if (process.env.NODE_ENV !== 'production') {
  ToggleButtonGroupButtonContext.displayName = 'ToggleButtonGroupButtonContext';
}
var _default = exports.default = ToggleButtonGroupButtonContext;