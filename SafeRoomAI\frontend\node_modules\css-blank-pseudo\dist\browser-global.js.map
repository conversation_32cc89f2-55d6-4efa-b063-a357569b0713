{"version": 3, "file": "browser-global.js", "sources": ["../src/browser-global.js", "../src/browser.js"], "sourcesContent": ["/* global self */\nimport { default as cssBlankPseudo } from './browser';\nself.cssBlankPseudo = cssBlankPseudo;\n", "/* global MutationObserver */\nexport default function cssBlankPseudo(document, opts) {\n\t// configuration\n\tconst className = Object(opts).className;\n\tconst attr = Object(opts).attr || 'blank';\n\tconst force = Object(opts).force;\n\n\ttry {\n\t\tdocument.querySelector(':blank');\n\n\t\tif (!force) {\n\t\t\treturn;\n\t\t}\n\t} catch (ignoredError) { /* do nothing and continue */ }\n\n\t// observe value changes on <input>, <select>, and <textarea>\n\tconst window = (document.ownerDocument || document).defaultView;\n\n\tobserveValueOfHTMLElement(window.HTMLInputElement);\n\tobserveValueOfHTMLElement(window.HTMLSelectElement);\n\tobserveValueOfHTMLElement(window.HTMLTextAreaElement);\n\tobserveSelectedOfHTMLElement(window.HTMLOptionElement);\n\n\t// form control elements selector\n\tconst selector = 'INPUT,SELECT,TEXTAREA';\n\tconst selectorRegExp = /^(INPUT|SELECT|TEXTAREA)$/;\n\n\t// conditionally update all form control elements\n\tArray.prototype.forEach.call(\n\t\tdocument.querySelectorAll(selector),\n\t\tnode => {\n\t\t\tif (node.nodeName === 'SELECT') {\n\t\t\t\tnode.addEventListener('change', configureCssBlankAttribute);\n\t\t\t} else {\n\t\t\t\tnode.addEventListener('input', configureCssBlankAttribute);\n\t\t\t}\n\n\t\t\tconfigureCssBlankAttribute.call(node);\n\t\t},\n\t);\n\n\t// conditionally observe added or unobserve removed form control elements\n\tnew MutationObserver(mutationsList => {\n\t\tmutationsList.forEach(mutation => {\n\t\t\tArray.prototype.forEach.call(\n\t\t\t\tmutation.addedNodes || [],\n\t\t\t\tnode => {\n\t\t\t\t\tif (node.nodeType === 1 && selectorRegExp.test(node.nodeName)) {\n\t\t\t\t\t\tif (node.nodeName === 'SELECT') {\n\t\t\t\t\t\t\tnode.addEventListener('change', configureCssBlankAttribute);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tnode.addEventListener('input', configureCssBlankAttribute);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconfigureCssBlankAttribute.call(node);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t);\n\n\t\t\tArray.prototype.forEach.call(\n\t\t\t\tmutation.removedNodes || [],\n\t\t\t\tnode => {\n\t\t\t\t\tif (node.nodeType === 1 && selectorRegExp.test(node.nodeName)) {\n\t\t\t\t\t\tif (node.nodeName === 'SELECT') {\n\t\t\t\t\t\t\tnode.removeEventListener('change', configureCssBlankAttribute);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tnode.removeEventListener('input', configureCssBlankAttribute);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t);\n\t\t});\n\t}).observe(document, { childList: true, subtree: true });\n\n\t// update a form control element’s css-blank attribute\n\tfunction configureCssBlankAttribute () {\n\t\tif (this.value || this.nodeName === 'SELECT' && this.options[this.selectedIndex].value) {\n\t\t\tif (attr) {\n\t\t\t\tthis.removeAttribute(attr);\n\t\t\t}\n\n\t\t\tif (className) {\n\t\t\t\tthis.classList.remove(className);\n\t\t\t}\n\t\t\tthis.removeAttribute('blank');\n\t\t} else {\n\t\t\tif (attr) {\n\t\t\t\tthis.setAttribute('blank', attr);\n\t\t\t}\n\n\t\t\tif (className) {\n\t\t\t\tthis.classList.add(className);\n\t\t\t}\n\t\t}\n\t}\n\n\t// observe changes to the \"value\" property on an HTML Element\n\tfunction observeValueOfHTMLElement (HTMLElement) {\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'value');\n\t\tconst nativeSet = descriptor.set;\n\n\t\tdescriptor.set = function set (value) { // eslint-disable-line no-unused-vars\n\t\t\tnativeSet.apply(this, arguments);\n\n\t\t\tconfigureCssBlankAttribute.apply(this);\n\t\t};\n\n\t\tObject.defineProperty(HTMLElement.prototype, 'value', descriptor);\n\t}\n\n\t// observe changes to the \"selected\" property on an HTML Element\n\tfunction observeSelectedOfHTMLElement (HTMLElement) {\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'selected');\n\t\tconst nativeSet = descriptor.set;\n\n\t\tdescriptor.set = function set (value) { // eslint-disable-line no-unused-vars\n\t\t\tnativeSet.apply(this, arguments);\n\n\t\t\tconst event = document.createEvent('Event');\n\t\t\tevent.initEvent('change', true, true);\n\t\t\tthis.dispatchEvent(event);\n\t\t};\n\n\t\tObject.defineProperty(HTMLElement.prototype, 'selected', descriptor);\n\t}\n}\n"], "names": ["self", "cssBlankPseudo", "document", "opts", "className", "Object", "attr", "force", "querySelector", "ignoredError", "HTMLElement", "descriptor", "nativeSet", "window", "ownerDocument", "defaultView", "observeValueOfHTMLElement", "HTMLInputElement", "HTMLSelectElement", "HTMLTextAreaElement", "HTMLOptionElement", "getOwnPropertyDescriptor", "prototype", "set", "value", "apply", "this", "arguments", "event", "createEvent", "initEvent", "dispatchEvent", "defineProperty", "selectorRegExp", "configureCssBlankAttribute", "nodeName", "options", "selectedIndex", "removeAttribute", "classList", "remove", "setAttribute", "add", "Array", "for<PERSON>ach", "call", "querySelectorAll", "node", "addEventListener", "MutationObserver", "mutationsList", "mutation", "addedNodes", "nodeType", "test", "removedNodes", "removeEventListener", "observe", "childList", "subtree"], "mappings": "AAEAA,KAAKC,eCDU,SAAwBC,EAAUC,OAE1CC,EAAYC,OAAOF,GAAMC,UACzBE,EAAOD,OAAOF,GAAMG,MAAQ,QAC5BC,EAAQF,OAAOF,GAAMI,aAG1BL,EAASM,cAAc,WAElBD,SAGJ,MAAOE,QAkG8BC,EAChCC,EACAC,EAjGDC,GAAUX,EAASY,eAAiBZ,GAAUa,YAEpDC,EAA0BH,EAAOI,kBACjCD,EAA0BH,EAAOK,mBACjCF,EAA0BH,EAAOM,qBA2FMT,EA1FVG,EAAOO,kBA2F7BT,EAAaN,OAAOgB,yBAAyBX,EAAYY,UAAW,YACpEV,EAAYD,EAAWY,IAE7BZ,EAAWY,IAAM,SAAcC,GAC9BZ,EAAUa,MAAMC,KAAMC,eAEhBC,EAAQ1B,EAAS2B,YAAY,SACnCD,EAAME,UAAU,UAAU,GAAM,QAC3BC,cAAcH,IAGpBvB,OAAO2B,eAAetB,EAAYY,UAAW,WAAYX,OAlGpDsB,EAAiB,qCAkDdC,IACJR,KAAKF,OAA2B,WAAlBE,KAAKS,UAAyBT,KAAKU,QAAQV,KAAKW,eAAeb,OAC5ElB,QACEgC,gBAAgBhC,GAGlBF,QACEmC,UAAUC,OAAOpC,QAElBkC,gBAAgB,WAEjBhC,QACEmC,aAAa,QAASnC,GAGxBF,QACEmC,UAAUG,IAAItC,aAMbY,EAA2BN,OAC7BC,EAAaN,OAAOgB,yBAAyBX,EAAYY,UAAW,SACpEV,EAAYD,EAAWY,IAE7BZ,EAAWY,IAAM,SAAcC,GAC9BZ,EAAUa,MAAMC,KAAMC,WAEtBO,EAA2BT,MAAMC,OAGlCrB,OAAO2B,eAAetB,EAAYY,UAAW,QAASX,GA/EvDgC,MAAMrB,UAAUsB,QAAQC,KACvB3C,EAAS4C,iBALO,0BAMhB,SAAAC,GACuB,WAAlBA,EAAKZ,SACRY,EAAKC,iBAAiB,SAAUd,GAEhCa,EAAKC,iBAAiB,QAASd,GAGhCA,EAA2BW,KAAKE,UAK9BE,kBAAiB,SAAAC,GACpBA,EAAcN,SAAQ,SAAAO,GACrBR,MAAMrB,UAAUsB,QAAQC,KACvBM,EAASC,YAAc,IACvB,SAAAL,GACuB,IAAlBA,EAAKM,UAAkBpB,EAAeqB,KAAKP,EAAKZ,YAC7B,WAAlBY,EAAKZ,SACRY,EAAKC,iBAAiB,SAAUd,GAEhCa,EAAKC,iBAAiB,QAASd,GAGhCA,EAA2BW,KAAKE,OAKnCJ,MAAMrB,UAAUsB,QAAQC,KACvBM,EAASI,cAAgB,IACzB,SAAAR,GACuB,IAAlBA,EAAKM,UAAkBpB,EAAeqB,KAAKP,EAAKZ,YAC7B,WAAlBY,EAAKZ,SACRY,EAAKS,oBAAoB,SAAUtB,GAEnCa,EAAKS,oBAAoB,QAAStB,aAMrCuB,QAAQvD,EAAU,CAAEwD,WAAW,EAAMC,SAAS"}