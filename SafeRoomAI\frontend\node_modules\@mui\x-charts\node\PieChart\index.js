"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _PieChart = require("./PieChart");
Object.keys(_PieChart).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _PieChart[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _PieChart[key];
    }
  });
});
var _PiePlot = require("./PiePlot");
Object.keys(_PiePlot).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _PiePlot[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _PiePlot[key];
    }
  });
});
var _PieArcPlot = require("./PieArcPlot");
Object.keys(_PieArcPlot).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _PieArcPlot[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _PieArcPlot[key];
    }
  });
});
var _PieArcLabelPlot = require("./PieArcLabelPlot");
Object.keys(_PieArcLabelPlot).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _PieArcLabelPlot[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _PieArcLabelPlot[key];
    }
  });
});
var _PieArc = require("./PieArc");
Object.keys(_PieArc).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _PieArc[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _PieArc[key];
    }
  });
});
var _PieArcLabel = require("./PieArcLabel");
Object.keys(_PieArcLabel).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _PieArcLabel[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _PieArcLabel[key];
    }
  });
});
var _getPieCoordinates = require("./getPieCoordinates");
Object.keys(_getPieCoordinates).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _getPieCoordinates[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _getPieCoordinates[key];
    }
  });
});