"""
LangChain Research Paper Analyzer Agent

This agent uses <PERSON><PERSON><PERSON>n to analyze academic research papers,
extract insights, and provide intelligent question-answering capabilities.
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# LangChain imports
from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS
from langchain.chains import RetrievalQA
from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.schema import Document

# Local imports
from config import Config
from utils import DocumentProcessor, TextAnalyzer, FileManager

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ResearchPaperAnalyzer:
    """
    LangChain-based Research Paper Analyzer Agent
    
    This agent can:
    - Load and process PDF research papers
    - Create vector embeddings for semantic search
    - Answer questions about research content
    - Generate summaries and identify research gaps
    - Analyze citations and extract insights
    """
    
    def __init__(self):
        """Initialize the Research Paper Analyzer"""
        self.config = Config()
        self.llm = ChatOpenAI(
            model_name=self.config.LLM_MODEL,
            temperature=self.config.TEMPERATURE,
            max_tokens=self.config.MAX_TOKENS
        )
        self.embeddings = OpenAIEmbeddings(model=self.config.EMBEDDING_MODEL)
        self.vector_store = None
        self.qa_chain = None
        self.documents = []
        self.current_paper_path = None
        self.current_paper_metadata = {}
        
        logger.info("Research Paper Analyzer initialized successfully")
    
    def load_paper(self, pdf_path: str) -> bool:
        """
        Load and process a research paper
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate PDF file
            if not FileManager.validate_pdf(pdf_path):
                raise ValueError(f"Invalid PDF file: {pdf_path}")
            
            logger.info(f"Loading research paper: {pdf_path}")
            
            # Extract metadata
            self.current_paper_metadata = DocumentProcessor.extract_metadata(pdf_path)
            self.current_paper_path = pdf_path
            
            # Load document using LangChain PDF loader
            loader = PyPDFLoader(pdf_path)
            documents = loader.load()
            
            # Split documents into chunks
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.config.CHUNK_SIZE,
                chunk_overlap=self.config.CHUNK_OVERLAP,
                length_function=len,
            )
            
            self.documents = text_splitter.split_documents(documents)
            
            # Create vector store
            self.vector_store = FAISS.from_documents(
                self.documents, 
                self.embeddings
            )
            
            # Create QA chain
            self.qa_chain = RetrievalQA.from_chain_type(
                llm=self.llm,
                chain_type="stuff",
                retriever=self.vector_store.as_retriever(
                    search_kwargs={"k": self.config.MAX_DOCS_TO_RETRIEVE}
                ),
                return_source_documents=True
            )
            
            logger.info(f"Successfully loaded paper with {len(self.documents)} chunks")
            return True
            
        except Exception as e:
            logger.error(f"Error loading paper: {e}")
            return False
    
    def ask(self, question: str) -> Dict[str, Any]:
        """
        Ask a question about the loaded research paper
        
        Args:
            question (str): Question to ask
            
        Returns:
            Dict[str, Any]: Response with answer and sources
        """
        if not self.qa_chain:
            raise ValueError("No paper loaded. Please load a paper first.")
        
        try:
            logger.info(f"Processing question: {question}")
            
            # Get response from QA chain
            response = self.qa_chain({"query": question})
            
            # Extract source information
            sources = []
            if "source_documents" in response:
                for doc in response["source_documents"]:
                    sources.append({
                        "content": doc.page_content[:200] + "...",
                        "metadata": doc.metadata
                    })
            
            return {
                "question": question,
                "answer": response["result"],
                "sources": sources,
                "confidence": "high" if len(sources) >= 3 else "medium"
            }
            
        except Exception as e:
            logger.error(f"Error processing question: {e}")
            return {
                "question": question,
                "answer": f"Error processing question: {e}",
                "sources": [],
                "confidence": "low"
            }

    def generate_summary(self) -> Dict[str, Any]:
        """
        Generate a comprehensive summary of the research paper

        Returns:
            Dict[str, Any]: Summary with key insights
        """
        if not self.documents:
            raise ValueError("No paper loaded. Please load a paper first.")

        try:
            logger.info("Generating research paper summary")

            # Combine first few chunks for summary (to get abstract and introduction)
            content = "\n".join([doc.page_content for doc in self.documents[:3]])

            # Create summary prompt
            prompt = PromptTemplate(
                input_variables=["content"],
                template=self.config.SUMMARY_PROMPT
            )

            # Generate summary
            summary_chain = prompt | self.llm
            summary = summary_chain.invoke({"content": content})

            # Extract additional insights
            token_count = TextAnalyzer.count_tokens(content)
            keywords = TextAnalyzer.extract_keywords(content)
            citations = TextAnalyzer.extract_citations(content)

            return {
                "summary": summary.content if hasattr(summary, 'content') else str(summary),
                "metadata": self.current_paper_metadata,
                "statistics": {
                    "total_chunks": len(self.documents),
                    "token_count": token_count,
                    "estimated_pages": self.current_paper_metadata.get('pages', 'Unknown'),
                    "citations_found": len(citations)
                },
                "keywords": keywords[:10],
                "file_info": {
                    "path": self.current_paper_path,
                    "size": FileManager.format_file_size(
                        FileManager.get_file_size(self.current_paper_path)
                    )
                }
            }

        except Exception as e:
            logger.error(f"Error generating summary: {e}")
            return {"error": f"Failed to generate summary: {e}"}

    def identify_research_gaps(self) -> Dict[str, Any]:
        """
        Identify potential research gaps and future opportunities

        Returns:
            Dict[str, Any]: Research gaps and opportunities
        """
        if not self.documents:
            raise ValueError("No paper loaded. Please load a paper first.")

        try:
            logger.info("Identifying research gaps")

            # Focus on conclusion and discussion sections
            relevant_chunks = []
            for doc in self.documents:
                content_lower = doc.page_content.lower()
                if any(keyword in content_lower for keyword in
                      ['conclusion', 'discussion', 'limitation', 'future', 'gap']):
                    relevant_chunks.append(doc.page_content)

            # If no specific sections found, use last few chunks
            if not relevant_chunks:
                relevant_chunks = [doc.page_content for doc in self.documents[-3:]]

            content = "\n".join(relevant_chunks)

            # Create research gap prompt
            prompt = PromptTemplate(
                input_variables=["content"],
                template=self.config.RESEARCH_GAP_PROMPT
            )

            # Generate research gaps analysis
            gap_chain = prompt | self.llm
            gaps = gap_chain.invoke({"content": content})

            return {
                "research_gaps": gaps.content if hasattr(gaps, 'content') else str(gaps),
                "analysis_scope": f"Analyzed {len(relevant_chunks)} relevant sections",
                "paper_title": self.current_paper_metadata.get('title', 'Unknown')
            }

        except Exception as e:
            logger.error(f"Error identifying research gaps: {e}")
            return {"error": f"Failed to identify research gaps: {e}"}

    def analyze_citations(self) -> Dict[str, Any]:
        """
        Analyze citations in the research paper

        Returns:
            Dict[str, Any]: Citation analysis results
        """
        if not self.documents:
            raise ValueError("No paper loaded. Please load a paper first.")

        try:
            logger.info("Analyzing citations")

            # Combine all document content
            full_content = "\n".join([doc.page_content for doc in self.documents])

            # Extract citations using utility function
            citations = TextAnalyzer.extract_citations(full_content)

            # Create citation analysis prompt
            prompt = PromptTemplate(
                input_variables=["content"],
                template=self.config.CITATION_ANALYSIS_PROMPT
            )

            # Generate citation analysis
            citation_chain = prompt | self.llm
            analysis = citation_chain.invoke({"content": full_content[:3000]})  # Limit content size

            return {
                "citation_analysis": analysis.content if hasattr(analysis, 'content') else str(analysis),
                "citations_extracted": citations[:20],  # Show first 20 citations
                "total_citations": len(citations),
                "paper_title": self.current_paper_metadata.get('title', 'Unknown')
            }

        except Exception as e:
            logger.error(f"Error analyzing citations: {e}")
            return {"error": f"Failed to analyze citations: {e}"}

    def get_paper_info(self) -> Dict[str, Any]:
        """
        Get information about the currently loaded paper

        Returns:
            Dict[str, Any]: Paper information
        """
        if not self.current_paper_path:
            return {"error": "No paper loaded"}

        return {
            "file_path": self.current_paper_path,
            "metadata": self.current_paper_metadata,
            "processing_info": {
                "chunks_created": len(self.documents),
                "vector_store_ready": self.vector_store is not None,
                "qa_chain_ready": self.qa_chain is not None
            },
            "file_stats": {
                "size": FileManager.format_file_size(
                    FileManager.get_file_size(self.current_paper_path)
                ),
                "valid_pdf": FileManager.validate_pdf(self.current_paper_path)
            }
        }
