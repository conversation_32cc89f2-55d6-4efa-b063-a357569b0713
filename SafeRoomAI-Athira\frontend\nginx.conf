# frontend/nginx.conf

server {
    # Listen on port 3000 inside the container.
    # (Compose maps host 3000 → container 3000.)
    listen 3000;
    server_name _;

    # Serve static files from this directory.
    root /usr/share/nginx/html;
    index index.html;

    # 1) Proxy anything under /predict/ to the backend API
    location /predict/ {
        proxy_pass         http://backend:8000/predict/;
        proxy_http_version 1.1;
        proxy_set_header   Upgrade $http_upgrade;
        proxy_set_header   Connection "upgrade";
        proxy_set_header   Host      $host;
        proxy_cache_bypass $http_upgrade;
    }

    # 2) Serve actual files if they exist, otherwise fall back to index.html
    #    This ensures React Router can handle client‐side routing.
    location / {
        try_files $uri /index.html;
    }