{"version": 3, "sources": ["lib/locale/ta/cdn.js"], "sourcesContent": ["(() => {\nvar _window$dateFns;function _typeof(o) {\"@babel/helpers - typeof\";return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, \"string\");return \"symbol\" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if (\"object\" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || \"default\");if (\"object\" != _typeof(i)) return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return (\"string\" === r ? String : Number)(t);}var __defProp = Object.defineProperty;\nvar __export = function __export(target, all) {\n  for (var name in all)\n  __defProp(target, name, {\n    get: all[name],\n    enumerable: true,\n    configurable: true,\n    set: function set(newValue) {return all[name] = function () {return newValue;};}\n  });\n};\n\n// lib/locale/ta/_lib/formatDistance.js\nfunction isPluralType(val) {\n  return val.one !== undefined;\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"\\u0B92\\u0BB0\\u0BC1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"\\u0B92\\u0BB0\\u0BC1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"\\u0B92\\u0BB0\\u0BC1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xSeconds: {\n    one: {\n      default: \"1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\",\n      in: \"1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0BAF\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BB5\\u0BBF\\u0BA8\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BB5\\u0BBF\\u0BA9\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BB5\\u0BBF\\u0BA8\\u0BBE\\u0B9F\\u0BBF\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  halfAMinute: {\n    default: \"\\u0B85\\u0BB0\\u0BC8 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD\",\n    in: \"\\u0B85\\u0BB0\\u0BC8 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n    ago: \"\\u0B85\\u0BB0\\u0BC8 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n  },\n  lessThanXMinutes: {\n    one: {\n      default: \"\\u0B92\\u0BB0\\u0BC1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"\\u0B92\\u0BB0\\u0BC1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"\\u0B92\\u0BB0\\u0BC1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0B95\\u0BC1\\u0BB1\\u0BC8\\u0BB5\\u0BBE\\u0B95\",\n      in: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BB3\\u0BCD\",\n      ago: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xMinutes: {\n    one: {\n      default: \"1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BA8\\u0BBF\\u0BAE\\u0BBF\\u0B9F\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\"\n    }\n  },\n  xHours: {\n    one: {\n      default: \"1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"{{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BAE\\u0BA3\\u0BBF \\u0BA8\\u0BC7\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xDays: {\n    one: {\n      default: \"1 \\u0BA8\\u0BBE\\u0BB3\\u0BCD\",\n      in: \"1 \\u0BA8\\u0BBE\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BA8\\u0BBE\\u0BB3\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BA8\\u0BBE\\u0B9F\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BA8\\u0BBE\\u0B9F\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BA8\\u0BBE\\u0B9F\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXWeeks: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BBE\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xWeeks: {\n    one: {\n      default: \"1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BB5\\u0BBE\\u0BB0\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BB5\\u0BBE\\u0BB0\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BB5\\u0BBE\\u0BB0\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXMonths: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BBE\\u0BA4\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BBE\\u0BA4\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BAE\\u0BBE\\u0BA4\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xMonths: {\n    one: {\n      default: \"1 \\u0BAE\\u0BBE\\u0BA4\\u0BAE\\u0BCD\",\n      in: \"1 \\u0BAE\\u0BBE\\u0BA4\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BAE\\u0BBE\\u0BA4\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0BAE\\u0BBE\\u0BA4\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B9A\\u0BC1\\u0BAE\\u0BBE\\u0BB0\\u0BCD {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  xYears: {\n    one: {\n      default: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"1 \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BCD\",\n      in: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  overXYears: {\n    one: {\n      default: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC7\\u0BB2\\u0BCD\",\n      in: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0BBF\\u0BB1\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0BAE\\u0BC7\\u0BB2\\u0BBE\\u0B95\",\n      ago: \"1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1\\u0BAE\\u0BCD \\u0BAE\\u0BC7\\u0BB2\\u0BBE\\u0B95\",\n      in: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"{{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  },\n  almostXYears: {\n    one: {\n      default: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD\",\n      in: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F 1 \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F 1 \\u0BB5\\u0BB0\\u0BC1\\u0B9F\\u0BAE\\u0BCD \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    },\n    other: {\n      default: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BCD\",\n      in: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BBF\\u0BB2\\u0BCD\",\n      ago: \"\\u0B95\\u0BBF\\u0B9F\\u0BCD\\u0B9F\\u0BA4\\u0BCD\\u0BA4\\u0B9F\\u0BCD\\u0B9F {{count}} \\u0B86\\u0BA3\\u0BCD\\u0B9F\\u0BC1\\u0B95\\u0BB3\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\\u0BAA\\u0BC1\"\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var tense = options !== null && options !== void 0 && options.addSuffix ? options.comparison && options.comparison > 0 ? \"in\" : \"ago\" : \"default\";\n  var tokenValue = formatDistanceLocale[token];\n  if (!isPluralType(tokenValue))\n  return tokenValue[tense];\n  if (count === 1) {\n    return tokenValue.one[tense];\n  } else {\n    return tokenValue.other[tense].replace(\"{{count}}\", String(count));\n  }\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/ta/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, d MMMM, y\",\n  long: \"d MMMM, y\",\n  medium: \"d MMM, y\",\n  short: \"d/M/yy\"\n};\nvar timeFormats = {\n  full: \"a h:mm:ss zzzz\",\n  long: \"a h:mm:ss z\",\n  medium: \"a h:mm:ss\",\n  short: \"a h:mm\"\n};\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};\n\n// lib/locale/ta/_lib/formatRelative.js\nvar formatRelativeLocale = {\n  lastWeek: \"'\\u0B95\\u0B9F\\u0BA8\\u0BCD\\u0BA4' eeee p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  yesterday: \"'\\u0BA8\\u0BC7\\u0BB1\\u0BCD\\u0BB1\\u0BC1 ' p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  today: \"'\\u0B87\\u0BA9\\u0BCD\\u0BB1\\u0BC1 ' p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  tomorrow: \"'\\u0BA8\\u0BBE\\u0BB3\\u0BC8 ' p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  nextWeek: \"eeee p '\\u0BAE\\u0BA3\\u0BBF\\u0B95\\u0BCD\\u0B95\\u0BC1'\",\n  other: \"P\"\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return function (value, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : \"standalone\";\n    var valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/ta/_lib/localize.js\nvar eraValues = {\n  narrow: [\"\\u0B95\\u0BBF.\\u0BAE\\u0BC1.\", \"\\u0B95\\u0BBF.\\u0BAA\\u0BBF.\"],\n  abbreviated: [\"\\u0B95\\u0BBF.\\u0BAE\\u0BC1.\", \"\\u0B95\\u0BBF.\\u0BAA\\u0BBF.\"],\n  wide: [\"\\u0B95\\u0BBF\\u0BB1\\u0BBF\\u0BB8\\u0BCD\\u0BA4\\u0BC1\\u0BB5\\u0BC1\\u0B95\\u0BCD\\u0B95\\u0BC1 \\u0BAE\\u0BC1\\u0BA9\\u0BCD\", \"\\u0B85\\u0BA9\\u0BCD\\u0BA9\\u0BCB \\u0B9F\\u0BCB\\u0BAE\\u0BBF\\u0BA9\\u0BBF\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"\\u0B95\\u0BBE\\u0BB2\\u0BBE.1\", \"\\u0B95\\u0BBE\\u0BB2\\u0BBE.2\", \"\\u0B95\\u0BBE\\u0BB2\\u0BBE.3\", \"\\u0B95\\u0BBE\\u0BB2\\u0BBE.4\"],\n  wide: [\n  \"\\u0B92\\u0BA9\\u0BCD\\u0BB1\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\",\n  \"\\u0B87\\u0BB0\\u0BA3\\u0BCD\\u0B9F\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\",\n  \"\\u0BAE\\u0BC2\\u0BA9\\u0BCD\\u0BB1\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\",\n  \"\\u0BA8\\u0BBE\\u0BA9\\u0BCD\\u0B95\\u0BBE\\u0BAE\\u0BCD \\u0B95\\u0BBE\\u0BB2\\u0BBE\\u0BA3\\u0BCD\\u0B9F\\u0BC1\"]\n\n};\nvar monthValues = {\n  narrow: [\"\\u0B9C\", \"\\u0BAA\\u0BBF\", \"\\u0BAE\\u0BBE\", \"\\u0B8F\", \"\\u0BAE\\u0BC7\", \"\\u0B9C\\u0BC2\", \"\\u0B9C\\u0BC2\", \"\\u0B86\", \"\\u0B9A\\u0BC6\", \"\\u0B85\", \"\\u0BA8\", \"\\u0B9F\\u0BBF\"],\n  abbreviated: [\n  \"\\u0B9C\\u0BA9.\",\n  \"\\u0BAA\\u0BBF\\u0BAA\\u0BCD.\",\n  \"\\u0BAE\\u0BBE\\u0BB0\\u0BCD.\",\n  \"\\u0B8F\\u0BAA\\u0BCD.\",\n  \"\\u0BAE\\u0BC7\",\n  \"\\u0B9C\\u0BC2\\u0BA9\\u0BCD\",\n  \"\\u0B9C\\u0BC2\\u0BB2\\u0BC8\",\n  \"\\u0B86\\u0B95.\",\n  \"\\u0B9A\\u0BC6\\u0BAA\\u0BCD.\",\n  \"\\u0B85\\u0B95\\u0BCD.\",\n  \"\\u0BA8\\u0BB5.\",\n  \"\\u0B9F\\u0BBF\\u0B9A.\"],\n\n  wide: [\n  \"\\u0B9C\\u0BA9\\u0BB5\\u0BB0\\u0BBF\",\n  \"\\u0BAA\\u0BBF\\u0BAA\\u0BCD\\u0BB0\\u0BB5\\u0BB0\\u0BBF\",\n  \"\\u0BAE\\u0BBE\\u0BB0\\u0BCD\\u0B9A\\u0BCD\",\n  \"\\u0B8F\\u0BAA\\u0BCD\\u0BB0\\u0BB2\\u0BCD\",\n  \"\\u0BAE\\u0BC7\",\n  \"\\u0B9C\\u0BC2\\u0BA9\\u0BCD\",\n  \"\\u0B9C\\u0BC2\\u0BB2\\u0BC8\",\n  \"\\u0B86\\u0B95\\u0BB8\\u0BCD\\u0B9F\\u0BCD\",\n  \"\\u0B9A\\u0BC6\\u0BAA\\u0BCD\\u0B9F\\u0BAE\\u0BCD\\u0BAA\\u0BB0\\u0BCD\",\n  \"\\u0B85\\u0B95\\u0BCD\\u0B9F\\u0BCB\\u0BAA\\u0BB0\\u0BCD\",\n  \"\\u0BA8\\u0BB5\\u0BAE\\u0BCD\\u0BAA\\u0BB0\\u0BCD\",\n  \"\\u0B9F\\u0BBF\\u0B9A\\u0BAE\\u0BCD\\u0BAA\\u0BB0\\u0BCD\"]\n\n};\nvar dayValues = {\n  narrow: [\"\\u0B9E\\u0BBE\", \"\\u0BA4\\u0BBF\", \"\\u0B9A\\u0BC6\", \"\\u0BAA\\u0BC1\", \"\\u0BB5\\u0BBF\", \"\\u0BB5\\u0BC6\", \"\\u0B9A\"],\n  short: [\"\\u0B9E\\u0BBE\", \"\\u0BA4\\u0BBF\", \"\\u0B9A\\u0BC6\", \"\\u0BAA\\u0BC1\", \"\\u0BB5\\u0BBF\", \"\\u0BB5\\u0BC6\", \"\\u0B9A\"],\n  abbreviated: [\"\\u0B9E\\u0BBE\\u0BAF\\u0BBF.\", \"\\u0BA4\\u0BBF\\u0B99\\u0BCD.\", \"\\u0B9A\\u0BC6\\u0BB5\\u0BCD.\", \"\\u0BAA\\u0BC1\\u0BA4.\", \"\\u0BB5\\u0BBF\\u0BAF\\u0BBE.\", \"\\u0BB5\\u0BC6\\u0BB3\\u0BCD.\", \"\\u0B9A\\u0BA9\\u0BBF\"],\n  wide: [\n  \"\\u0B9E\\u0BBE\\u0BAF\\u0BBF\\u0BB1\\u0BC1\",\n  \"\\u0BA4\\u0BBF\\u0B99\\u0BCD\\u0B95\\u0BB3\\u0BCD\",\n  \"\\u0B9A\\u0BC6\\u0BB5\\u0BCD\\u0BB5\\u0BBE\\u0BAF\\u0BCD\",\n  \"\\u0BAA\\u0BC1\\u0BA4\\u0BA9\\u0BCD\",\n  \"\\u0BB5\\u0BBF\\u0BAF\\u0BBE\\u0BB4\\u0BA9\\u0BCD\",\n  \"\\u0BB5\\u0BC6\\u0BB3\\u0BCD\\u0BB3\\u0BBF\",\n  \"\\u0B9A\\u0BA9\\u0BBF\"]\n\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: \"\\u0BAE\\u0BC1.\\u0BAA\",\n    pm: \"\\u0BAA\\u0BBF.\\u0BAA\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD.\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD.\",\n    morning: \"\\u0B95\\u0BBE.\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF.\",\n    evening: \"\\u0BAE\\u0BBE.\",\n    night: \"\\u0B87\\u0BB0.\"\n  },\n  abbreviated: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  },\n  wide: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: \"\\u0BAE\\u0BC1.\\u0BAA\",\n    pm: \"\\u0BAA\\u0BBF.\\u0BAA\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD.\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD.\",\n    morning: \"\\u0B95\\u0BBE.\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF.\",\n    evening: \"\\u0BAE\\u0BBE.\",\n    night: \"\\u0B87\\u0BB0.\"\n  },\n  abbreviated: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  },\n  wide: {\n    am: \"\\u0BAE\\u0BC1\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    pm: \"\\u0BAA\\u0BBF\\u0BB1\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    midnight: \"\\u0BA8\\u0BB3\\u0BCD\\u0BB3\\u0BBF\\u0BB0\\u0BB5\\u0BC1\",\n    noon: \"\\u0BA8\\u0BA3\\u0BCD\\u0BAA\\u0B95\\u0BB2\\u0BCD\",\n    morning: \"\\u0B95\\u0BBE\\u0BB2\\u0BC8\",\n    afternoon: \"\\u0BAE\\u0BA4\\u0BBF\\u0BAF\\u0BAE\\u0BCD\",\n    evening: \"\\u0BAE\\u0BBE\\u0BB2\\u0BC8\",\n    night: \"\\u0B87\\u0BB0\\u0BB5\\u0BC1\"\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  return String(dirtyNumber);\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n    return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n    return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\n\n// lib/locale/ta/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)(வது)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(கி.மு.|கி.பி.)/i,\n  abbreviated: /^(கி\\.?\\s?மு\\.?|கி\\.?\\s?பி\\.?)/,\n  wide: /^(கிறிஸ்துவுக்கு\\sமுன்|அன்னோ\\sடோமினி)/i\n};\nvar parseEraPatterns = {\n  any: [/கி\\.?\\s?மு\\.?/, /கி\\.?\\s?பி\\.?/]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^காலா.[1234]/i,\n  wide: /^(ஒன்றாம்|இரண்டாம்|மூன்றாம்|நான்காம்) காலாண்டு/i\n};\nvar parseQuarterPatterns = {\n  narrow: [/1/i, /2/i, /3/i, /4/i],\n  any: [\n  /(1|காலா.1|ஒன்றாம்)/i,\n  /(2|காலா.2|இரண்டாம்)/i,\n  /(3|காலா.3|மூன்றாம்)/i,\n  /(4|காலா.4|நான்காம்)/i]\n\n};\nvar matchMonthPatterns = {\n  narrow: /^(ஜ|பி|மா|ஏ|மே|ஜூ|ஆ|செ|அ|ந|டி)$/i,\n  abbreviated: /^(ஜன.|பிப்.|மார்.|ஏப்.|மே|ஜூன்|ஜூலை|ஆக.|செப்.|அக்.|நவ.|டிச.)/i,\n  wide: /^(ஜனவரி|பிப்ரவரி|மார்ச்|ஏப்ரல்|மே|ஜூன்|ஜூலை|ஆகஸ்ட்|செப்டம்பர்|அக்டோபர்|நவம்பர்|டிசம்பர்)/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n  /^ஜ$/i,\n  /^பி/i,\n  /^மா/i,\n  /^ஏ/i,\n  /^மே/i,\n  /^ஜூ/i,\n  /^ஜூ/i,\n  /^ஆ/i,\n  /^செ/i,\n  /^அ/i,\n  /^ந/i,\n  /^டி/i],\n\n  any: [\n  /^ஜன/i,\n  /^பி/i,\n  /^மா/i,\n  /^ஏ/i,\n  /^மே/i,\n  /^ஜூன்/i,\n  /^ஜூலை/i,\n  /^ஆ/i,\n  /^செ/i,\n  /^அ/i,\n  /^ந/i,\n  /^டி/i]\n\n};\nvar matchDayPatterns = {\n  narrow: /^(ஞா|தி|செ|பு|வி|வெ|ச)/i,\n  short: /^(ஞா|தி|செ|பு|வி|வெ|ச)/i,\n  abbreviated: /^(ஞாயி.|திங்.|செவ்.|புத.|வியா.|வெள்.|சனி)/i,\n  wide: /^(ஞாயிறு|திங்கள்|செவ்வாய்|புதன்|வியாழன்|வெள்ளி|சனி)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^ஞா/i, /^தி/i, /^செ/i, /^பு/i, /^வி/i, /^வெ/i, /^ச/i],\n  any: [/^ஞா/i, /^தி/i, /^செ/i, /^பு/i, /^வி/i, /^வெ/i, /^ச/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(மு.ப|பி.ப|நள்|நண்|காலை|மதியம்|மாலை|இரவு)/i,\n  any: /^(மு.ப|பி.ப|முற்பகல்|பிற்பகல்|நள்ளிரவு|நண்பகல்|காலை|மதியம்|மாலை|இரவு)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^மு/i,\n    pm: /^பி/i,\n    midnight: /^நள்/i,\n    noon: /^நண்/i,\n    morning: /காலை/i,\n    afternoon: /மதியம்/i,\n    evening: /மாலை/i,\n    night: /இரவு/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {return parseInt(value, 10);}\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: function valueCallback(index) {return index + 1;}\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/ta.js\nvar ta = {\n  code: \"ta\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 4\n  }\n};\n\n// lib/locale/ta/cdn.js\nwindow.dateFns = _objectSpread(_objectSpread({},\nwindow.dateFns), {}, {\n  locale: _objectSpread(_objectSpread({}, (_window$dateFns =\n  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {\n    ta: ta }) });\n\n\n\n//# debugId=37224F769AD45E0A64756E2164756E21\n\n//# sourceMappingURL=cdn.js.map\n})();"], "mappings": "AAAA,CAAC,IAAM,CACP,IAAI,EAAgB,SAAS,CAAO,CAAC,EAAG,CAA2B,OAAO,SAA+B,QAArB,mBAAkD,OAAO,UAA1B,iBAA8C,CAAC,EAAG,CAAC,cAAc,WAAe,CAAC,EAAG,CAAC,OAAO,UAA0B,QAArB,YAA+B,EAAE,cAAgB,QAAU,IAAM,OAAO,UAAY,gBAAkB,GAAK,EAAQ,CAAC,EAAG,SAAS,CAAO,CAAC,EAAG,EAAG,CAAC,IAAI,EAAI,OAAO,KAAK,CAAC,EAAE,GAAI,OAAO,sBAAuB,CAAC,IAAI,EAAI,OAAO,sBAAsB,CAAC,EAAE,IAAM,EAAI,EAAE,eAAgB,CAAC,EAAG,CAAC,OAAO,OAAO,yBAAyB,EAAG,CAAC,EAAE,WAAY,GAAI,EAAE,KAAK,MAAM,EAAG,CAAC,EAAG,OAAO,EAAG,SAAS,CAAa,CAAC,EAAG,CAAC,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAC,IAAI,EAAY,UAAU,IAAlB,KAAuB,UAAU,GAAK,CAAC,EAAE,EAAI,EAAI,EAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,gBAAiB,CAAC,EAAG,CAAC,EAAgB,EAAG,EAAG,EAAE,EAAE,EAAG,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI,EAAQ,OAAO,CAAC,CAAC,EAAE,gBAAiB,CAAC,EAAG,CAAC,OAAO,eAAe,EAAG,EAAG,OAAO,yBAAyB,EAAG,CAAC,CAAC,EAAG,EAAG,OAAO,EAAG,SAAS,CAAe,CAAC,EAAK,EAAK,EAAO,CAA2B,GAA1B,EAAM,EAAe,CAAG,EAAM,KAAO,EAAM,OAAO,eAAe,EAAK,EAAK,CAAE,MAAO,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,MAAU,GAAI,GAAO,EAAO,OAAO,EAAK,SAAS,CAAc,CAAC,EAAG,CAAC,IAAI,EAAI,EAAa,EAAG,QAAQ,EAAE,OAAmB,EAAQ,CAAC,GAArB,SAAyB,EAAI,OAAO,CAAC,EAAG,SAAS,CAAY,CAAC,EAAG,EAAG,CAAC,GAAgB,EAAQ,CAAC,GAArB,WAA2B,EAAG,OAAO,EAAE,IAAI,EAAI,EAAE,OAAO,aAAa,GAAe,IAAN,OAAS,CAAC,IAAI,EAAI,EAAE,KAAK,EAAG,GAAK,SAAS,EAAE,GAAgB,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAE,MAAM,IAAI,UAAU,8CAA8C,EAAG,OAAqB,IAAb,SAAiB,OAAS,QAAQ,CAAC,EAAG,IAAI,EAAY,OAAO,eACroD,YAAoB,CAAQ,CAAC,EAAQ,EAAK,CAC5C,QAAS,KAAQ,EACjB,EAAU,EAAQ,EAAM,CACtB,IAAK,EAAI,GACT,WAAY,GACZ,aAAc,GACd,aAAc,CAAG,CAAC,EAAU,CAAC,OAAO,EAAI,WAAiB,EAAG,CAAC,OAAO,GACtE,CAAC,GAIH,SAAS,CAAY,CAAC,EAAK,CACzB,OAAO,EAAI,MAAQ,OAErB,IAAI,EAAuB,CACzB,iBAAkB,CAChB,IAAK,CACH,QAAS,6HACT,GAAI,8FACJ,IAAK,sHACP,EACA,MAAO,CACL,QAAS,sIACT,GAAI,uGACJ,IAAK,+HACP,CACF,EACA,SAAU,CACR,IAAK,CACH,QAAS,yCACT,GAAI,iEACJ,IAAK,6EACP,EACA,MAAO,CACL,QAAS,mEACT,GAAI,+EACJ,IAAK,+HACP,CACF,EACA,YAAa,CACX,QAAS,gEACT,GAAI,wFACJ,IAAK,oGACP,EACA,iBAAkB,CAChB,IAAK,CACH,QAAS,2JACT,GAAI,gHACJ,IAAK,wIACP,EACA,MAAO,CACL,QAAS,wJACT,GAAI,6GACJ,IAAK,qIACP,CACF,EACA,SAAU,CACR,IAAK,CACH,QAAS,+CACT,GAAI,uEACJ,IAAK,mFACP,EACA,MAAO,CACL,QAAS,yEACT,GAAI,qFACJ,IAAK,qIACP,CACF,EACA,YAAa,CACX,IAAK,CACH,QAAS,2FACT,GAAI,mHACJ,IAAK,mKACP,EACA,MAAO,CACL,QAAS,mGACT,GAAI,4KACJ,IAAK,0HACP,CACF,EACA,OAAQ,CACN,IAAK,CACH,QAAS,sDACT,GAAI,8EACJ,IAAK,8HACP,EACA,MAAO,CACL,QAAS,8DACT,GAAI,sFACJ,IAAK,sIACP,CACF,EACA,MAAO,CACL,IAAK,CACH,QAAS,6BACT,GAAI,yCACJ,IAAK,iEACP,EACA,MAAO,CACL,QAAS,uDACT,GAAI,mEACJ,IAAK,mHACP,CACF,EACA,YAAa,CACX,IAAK,CACH,QAAS,wEACT,GAAI,gGACJ,IAAK,4GACP,EACA,MAAO,CACL,QAAS,kGACT,GAAI,8GACJ,IAAK,8JACP,CACF,EACA,OAAQ,CACN,IAAK,CACH,QAAS,mCACT,GAAI,2DACJ,IAAK,uEACP,EACA,MAAO,CACL,QAAS,6DACT,GAAI,yEACJ,IAAK,yHACP,CACF,EACA,aAAc,CACZ,IAAK,CACH,QAAS,wEACT,GAAI,gGACJ,IAAK,gJACP,EACA,MAAO,CACL,QAAS,kGACT,GAAI,8GACJ,IAAK,8JACP,CACF,EACA,QAAS,CACP,IAAK,CACH,QAAS,mCACT,GAAI,2DACJ,IAAK,uEACP,EACA,MAAO,CACL,QAAS,6DACT,GAAI,yEACJ,IAAK,yHACP,CACF,EACA,YAAa,CACX,IAAK,CACH,QAAS,8EACT,GAAI,oFACJ,IAAK,kHACP,EACA,MAAO,CACL,QAAS,kGACT,GAAI,8GACJ,IAAK,8JACP,CACF,EACA,OAAQ,CACN,IAAK,CACH,QAAS,yCACT,GAAI,+CACJ,IAAK,6EACP,EACA,MAAO,CACL,QAAS,6DACT,GAAI,yEACJ,IAAK,yHACP,CACF,EACA,WAAY,CACV,IAAK,CACH,QAAS,sGACT,GAAI,wHACJ,IAAK,6EACP,EACA,MAAO,CACL,QAAS,gIACT,GAAI,yEACJ,IAAK,yHACP,CACF,EACA,aAAc,CACZ,IAAK,CACH,QAAS,4GACT,GAAI,kHACJ,IAAK,gJACP,EACA,MAAO,CACL,QAAS,gIACT,GAAI,4IACJ,IAAK,4LACP,CACF,CACF,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAS,CAClE,IAAI,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UAAY,EAAQ,YAAc,EAAQ,WAAa,EAAI,KAAO,MAAQ,UACpI,EAAa,EAAqB,GACtC,IAAK,EAAa,CAAU,EAC5B,OAAO,EAAW,GAClB,GAAI,IAAU,EACZ,OAAO,EAAW,IAAI,OAEtB,QAAO,EAAW,MAAM,GAAO,QAAQ,YAAa,OAAO,CAAK,CAAC,GAKrE,SAAS,CAAiB,CAAC,EAAM,CAC/B,eAAgB,EAAG,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACjG,EAAQ,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACrD,EAAS,EAAK,QAAQ,IAAU,EAAK,QAAQ,EAAK,cACtD,OAAO,GAKX,IAAI,EAAc,CAChB,KAAM,kBACN,KAAM,YACN,OAAQ,WACR,MAAO,QACT,EACI,EAAc,CAChB,KAAM,iBACN,KAAM,cACN,OAAQ,YACR,MAAO,QACT,EACI,EAAkB,CACpB,KAAM,oBACN,KAAM,oBACN,OAAQ,qBACR,MAAO,oBACT,EACI,EAAa,CACf,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,SAAU,EAAkB,CAC1B,QAAS,EACT,aAAc,MAChB,CAAC,CACH,EAGI,EAAuB,CACzB,SAAU,uFACV,UAAW,yFACX,MAAO,mFACP,SAAU,6EACV,SAAU,sDACV,MAAO,GACT,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAW,EAAU,CAAC,OAAO,EAAqB,IAG7G,SAAS,CAAe,CAAC,EAAM,CAC7B,eAAgB,CAAC,EAAO,EAAS,CAC/B,IAAI,EAAU,IAAY,MAAQ,IAAiB,QAAK,EAAQ,QAAU,OAAO,EAAQ,OAAO,EAAI,aAChG,EACJ,GAAI,IAAY,cAAgB,EAAK,iBAAkB,CACrD,IAAI,EAAe,EAAK,wBAA0B,EAAK,aACnD,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAC9F,EAAc,EAAK,iBAAiB,IAAU,EAAK,iBAAiB,OAC/D,CACL,IAAI,EAAgB,EAAK,aACrB,EAAS,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACpG,EAAc,EAAK,OAAO,IAAW,EAAK,OAAO,GAEnD,IAAI,EAAQ,EAAK,iBAAmB,EAAK,iBAAiB,CAAK,EAAI,EACnE,OAAO,EAAY,IAKvB,IAAI,EAAY,CACd,OAAQ,CAAC,6BAA8B,4BAA4B,EACnE,YAAa,CAAC,6BAA8B,4BAA4B,EACxE,KAAM,CAAC,gHAAiH,qEAAqE,CAC/L,EACI,EAAgB,CAClB,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,6BAA8B,6BAA8B,6BAA8B,4BAA4B,EACpI,KAAM,CACN,8FACA,oGACA,oGACA,mGAAmG,CAErG,EACI,EAAc,CAChB,OAAQ,CAAC,SAAU,eAAgB,eAAgB,SAAU,eAAgB,eAAgB,eAAgB,SAAU,eAAgB,SAAU,SAAU,cAAc,EACzK,YAAa,CACb,gBACA,4BACA,4BACA,sBACA,eACA,2BACA,2BACA,gBACA,4BACA,sBACA,gBACA,qBAAqB,EAErB,KAAM,CACN,iCACA,mDACA,uCACA,uCACA,eACA,2BACA,2BACA,uCACA,+DACA,mDACA,6CACA,kDAAkD,CAEpD,EACI,EAAY,CACd,OAAQ,CAAC,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,QAAQ,EACjH,MAAO,CAAC,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,QAAQ,EAChH,YAAa,CAAC,4BAA6B,4BAA6B,4BAA6B,sBAAuB,4BAA6B,4BAA6B,oBAAoB,EAC1M,KAAM,CACN,uCACA,6CACA,mDACA,iCACA,6CACA,uCACA,oBAAoB,CAEtB,EACI,EAAkB,CACpB,OAAQ,CACN,GAAI,sBACJ,GAAI,sBACJ,SAAU,sBACV,KAAM,sBACN,QAAS,gBACT,UAAW,sBACX,QAAS,gBACT,MAAO,eACT,EACA,YAAa,CACX,GAAI,mDACJ,GAAI,mDACJ,SAAU,mDACV,KAAM,6CACN,QAAS,2BACT,UAAW,uCACX,QAAS,2BACT,MAAO,0BACT,EACA,KAAM,CACJ,GAAI,mDACJ,GAAI,mDACJ,SAAU,mDACV,KAAM,6CACN,QAAS,2BACT,UAAW,uCACX,QAAS,2BACT,MAAO,0BACT,CACF,EACI,EAA4B,CAC9B,OAAQ,CACN,GAAI,sBACJ,GAAI,sBACJ,SAAU,sBACV,KAAM,sBACN,QAAS,gBACT,UAAW,sBACX,QAAS,gBACT,MAAO,eACT,EACA,YAAa,CACX,GAAI,mDACJ,GAAI,mDACJ,SAAU,mDACV,KAAM,6CACN,QAAS,2BACT,UAAW,uCACX,QAAS,2BACT,MAAO,0BACT,EACA,KAAM,CACJ,GAAI,mDACJ,GAAI,mDACJ,SAAU,mDACV,KAAM,6CACN,QAAS,2BACT,UAAW,uCACX,QAAS,2BACT,MAAO,0BACT,CACF,EACI,WAAyB,CAAa,CAAC,EAAa,EAAU,CAChE,OAAO,OAAO,CAAW,GAEvB,EAAW,CACb,cAAe,EACf,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,QAAS,EAAgB,CACvB,OAAQ,EACR,aAAc,OACd,0BAA2B,CAAgB,CAAC,EAAS,CAAC,OAAO,EAAU,EACzE,CAAC,EACD,MAAO,EAAgB,CACrB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,UAAW,EAAgB,CACzB,OAAQ,EACR,aAAc,OACd,iBAAkB,EAClB,uBAAwB,MAC1B,CAAC,CACH,EAGA,SAAS,CAAY,CAAC,EAAM,CAC1B,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAQ,EAAQ,MAChB,EAAe,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC7E,EAAc,EAAO,MAAM,CAAY,EAC3C,IAAK,EACH,OAAO,KAET,IAAI,EAAgB,EAAY,GAC5B,EAAgB,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC9E,EAAM,MAAM,QAAQ,CAAa,EAAI,EAAU,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EAAI,EAAQ,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EACzM,EACJ,EAAQ,EAAK,cAAgB,EAAK,cAAc,CAAG,EAAI,EACvD,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,GAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,EAAK,GAGtC,SAAS,CAAO,CAAC,EAAQ,EAAW,CAClC,QAAS,KAAO,EACd,GAAI,OAAO,UAAU,eAAe,KAAK,EAAQ,CAAG,GAAK,EAAU,EAAO,EAAI,EAC5E,OAAO,EAGX,OAEF,SAAS,CAAS,CAAC,EAAO,EAAW,CACnC,QAAS,EAAM,EAAG,EAAM,EAAM,OAAQ,IACpC,GAAI,EAAU,EAAM,EAAI,EACtB,OAAO,EAGX,OAIF,SAAS,CAAmB,CAAC,EAAM,CACjC,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAgB,EAAY,GAC5B,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAQ,EAAK,cAAgB,EAAK,cAAc,EAAY,EAAE,EAAI,EAAY,GAClF,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAKtC,IAAI,EAA4B,gBAC5B,EAA4B,OAC5B,EAAmB,CACrB,OAAQ,oBACR,YAAa,iCACb,KAAM,wCACR,EACI,EAAmB,CACrB,IAAK,CAAC,gBAAgB,eAAe,CACvC,EACI,EAAuB,CACzB,OAAQ,WACR,YAAa,gBACb,KAAM,iDACR,EACI,EAAuB,CACzB,OAAQ,CAAC,KAAM,KAAM,KAAM,IAAI,EAC/B,IAAK,CACL,sBACA,uBACA,uBACA,sBAAqB,CAEvB,EACI,EAAqB,CACvB,OAAQ,mCACR,YAAa,gEACb,KAAM,2FACR,EACI,EAAqB,CACvB,OAAQ,CACR,OACA,OACA,OACA,MACA,OACA,OACA,OACA,MACA,OACA,MACA,MACA,MAAK,EAEL,IAAK,CACL,OACA,OACA,OACA,MACA,OACA,SACA,SACA,MACA,OACA,MACA,MACA,MAAK,CAEP,EACI,EAAmB,CACrB,OAAQ,0BACR,MAAO,0BACP,YAAa,6CACb,KAAM,sDACR,EACI,EAAmB,CACrB,OAAQ,CAAC,OAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,KAAK,EAC7D,IAAK,CAAC,OAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,KAAK,CAC5D,EACI,EAAyB,CAC3B,OAAQ,8CACR,IAAK,wEACP,EACI,EAAyB,CAC3B,IAAK,CACH,GAAI,OACJ,GAAI,OACJ,SAAU,QACV,KAAM,QACN,QAAS,QACT,UAAW,UACX,QAAS,QACT,MAAO,OACT,CACF,EACI,GAAQ,CACV,cAAe,EAAoB,CACjC,aAAc,EACd,aAAc,EACd,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,SAAS,EAAO,EAAE,EACzE,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,QAAS,EAAa,CACpB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACnB,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,EAAQ,EAC/D,CAAC,EACD,MAAO,EAAa,CAClB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,UAAW,EAAa,CACtB,cAAe,EACf,kBAAmB,MACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,CACH,EAGI,GAAK,CACP,KAAM,KACN,eAAgB,EAChB,WAAY,EACZ,eAAgB,EAChB,SAAU,EACV,MAAO,GACP,QAAS,CACP,aAAc,EACd,sBAAuB,CACzB,CACF,EAGA,OAAO,QAAU,EAAc,EAAc,CAAC,EAC9C,OAAO,OAAO,EAAG,CAAC,EAAG,CACnB,OAAQ,EAAc,EAAc,CAAC,GAAI,EACzC,OAAO,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,MAAM,EAAG,CAAC,EAAG,CAC7F,GAAI,EAAG,CAAC,CAAE,CAAC,IAOZ", "debugId": "576FF273D438B73664756E2164756E21", "names": []}