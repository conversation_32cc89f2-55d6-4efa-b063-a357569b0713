"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.usePickerLayoutProps = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _RtlProvider = require("@mui/system/RtlProvider");
var _useIsLandscape = require("../useIsLandscape");
/**
 * Props used to create the layout of the views.
 * Those props are exposed on all the pickers.
 */

/**
 * Prepare the props for the view layout (managed by `PickersLayout`)
 */
const usePickerLayoutProps = ({
  props,
  propsFromPickerValue,
  propsFromPickerViews,
  wrapperVariant
}) => {
  const {
    orientation
  } = props;
  const isLandscape = (0, _useIsLandscape.useIsLandscape)(propsFromPickerViews.views, orientation);
  const isRtl = (0, _RtlProvider.useRtl)();
  const layoutProps = (0, _extends2.default)({}, propsFromPickerViews, propsFromPickerValue, {
    isLandscape,
    isRtl,
    wrapperVariant,
    disabled: props.disabled,
    readOnly: props.readOnly
  });
  return {
    layoutProps
  };
};
exports.usePickerLayoutProps = usePickerLayoutProps;