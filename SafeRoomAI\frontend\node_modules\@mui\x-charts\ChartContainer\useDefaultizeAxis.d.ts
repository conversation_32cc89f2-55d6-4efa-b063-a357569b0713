import { MakeOptional } from '../models/helpers';
import { AxisConfig, ScaleName } from '../models';
import { ChartsAxisProps } from '../models/axis';
import { DatasetType } from '../models/seriesType/config';
export declare const useDefaultizeAxis: (inXAxis: MakeOptional<AxisConfig<ScaleName, any, ChartsAxisProps>, "id">[] | undefined, inYAxis: MakeOptional<AxisConfig<ScaleName, any, ChartsAxisProps>, "id">[] | undefined, dataset: DatasetType | undefined) => {
    reverse?: boolean | undefined;
    valueFormatter?: ((value: any, context: import("../internals").AxisValueFormatterContext) => string) | undefined;
    data?: any[] | undefined;
    dataKey?: string | undefined;
    label?: string | undefined;
    min?: (number | Date) | undefined;
    max?: (number | Date) | undefined;
    tickMaxStep?: number | undefined;
    tickMinStep?: number | undefined;
    tickNumber?: number | undefined;
    disableLine?: boolean | undefined;
    disableTicks?: boolean | undefined;
    fill?: string | undefined;
    tickFontSize?: number | undefined;
    tickLabelStyle?: import("..").ChartsTextStyle | undefined;
    labelStyle?: import("..").ChartsTextStyle | undefined;
    tickLabelInterval?: "auto" | ((value: any, index: number) => boolean) | undefined;
    labelFontSize?: number | undefined;
    stroke?: string | undefined;
    tickSize?: number | undefined;
    classes?: Partial<import("..").ChartsAxisClasses> | undefined;
    slots?: Partial<import("../internals").ChartsAxisSlots> | undefined;
    slotProps?: Partial<import("../internals").ChartsAxisSlotProps> | undefined;
    sx?: import("@mui/system").SxProps | undefined;
    tickInterval?: "auto" | ((value: any, index: number) => boolean) | any[] | undefined;
    tickPlacement?: "start" | "end" | "middle" | "extremities" | undefined;
    tickLabelPlacement?: "middle" | "tick" | undefined;
    scaleType?: "linear" | "band" | "point" | "log" | "pow" | "sqrt" | "time" | "utc" | undefined;
    colorMap?: import("../models/colorMapping").ContinuousColorConfig<number | Date> | import("../models/colorMapping").PiecewiseColorConfig<number | Date> | import("../models/colorMapping").OrdinalColorConfig<string | number | Date> | undefined;
    hideTooltip?: boolean | undefined;
    domainLimit?: "nice" | "strict" | ((min: number, max: number) => {
        min: number;
        max: number;
    }) | undefined;
    id: import("../internals").AxisId;
}[][];
