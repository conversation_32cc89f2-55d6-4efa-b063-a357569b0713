/**
 * Clears all of the internal caches.
 * Generally you shouldn't need or want to use this.
 * Examples of intended uses:
 * - In tests to reset parser state to keep tests isolated.
 * - In custom lint tooling that iteratively lints one project at a time to prevent OOMs.
 */
export declare function clearCaches(): void;
export declare const clearProgramCache: typeof clearCaches;
//# sourceMappingURL=clear-caches.d.ts.map
