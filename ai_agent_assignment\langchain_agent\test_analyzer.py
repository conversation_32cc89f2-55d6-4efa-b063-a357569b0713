"""
Test script for the Research Paper Analyzer Agent
"""

import os
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from research_analyzer import ResearchPaperAnalyzer
from utils import <PERSON>Manager

def test_analyzer():
    """Test the Research Paper Analyzer functionality"""
    
    print("🔬 Testing Research Paper Analyzer Agent")
    print("=" * 50)
    
    # Initialize analyzer
    try:
        analyzer = ResearchPaperAnalyzer()
        print("✅ Analyzer initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize analyzer: {e}")
        return
    
    # Test with sample paper (you would need to add a real PDF)
    sample_paper_path = "./sample_papers/sample_research_paper.pdf"
    
    if not os.path.exists(sample_paper_path):
        print(f"⚠️  Sample paper not found at {sample_paper_path}")
        print("Please add a sample PDF research paper to test with.")
        return
    
    # Test paper loading
    print(f"\n📄 Testing paper loading...")
    success = analyzer.load_paper(sample_paper_path)
    
    if success:
        print("✅ Paper loaded successfully")
    else:
        print("❌ Failed to load paper")
        return
    
    # Test question answering
    print(f"\n💬 Testing question answering...")
    test_questions = [
        "What is the main research question?",
        "What methodology was used?",
        "What are the key findings?",
        "What are the limitations of this study?"
    ]
    
    for question in test_questions:
        print(f"\nQ: {question}")
        try:
            response = analyzer.ask(question)
            print(f"A: {response['answer'][:200]}...")
            print(f"Confidence: {response['confidence']}")
            print(f"Sources: {len(response['sources'])}")
        except Exception as e:
            print(f"❌ Error: {e}")
    
    # Test summary generation
    print(f"\n📝 Testing summary generation...")
    try:
        summary = analyzer.generate_summary()
        if 'error' not in summary:
            print("✅ Summary generated successfully")
            print(f"Summary length: {len(summary['summary'])} characters")
            print(f"Keywords found: {len(summary.get('keywords', []))}")
        else:
            print(f"❌ Summary error: {summary['error']}")
    except Exception as e:
        print(f"❌ Summary generation failed: {e}")
    
    # Test research gap analysis
    print(f"\n🔍 Testing research gap analysis...")
    try:
        gaps = analyzer.identify_research_gaps()
        if 'error' not in gaps:
            print("✅ Research gaps identified successfully")
            print(f"Analysis scope: {gaps.get('analysis_scope', 'Unknown')}")
        else:
            print(f"❌ Research gap error: {gaps['error']}")
    except Exception as e:
        print(f"❌ Research gap analysis failed: {e}")
    
    # Test citation analysis
    print(f"\n📚 Testing citation analysis...")
    try:
        citations = analyzer.analyze_citations()
        if 'error' not in citations:
            print("✅ Citation analysis completed successfully")
            print(f"Total citations: {citations.get('total_citations', 0)}")
        else:
            print(f"❌ Citation analysis error: {citations['error']}")
    except Exception as e:
        print(f"❌ Citation analysis failed: {e}")
    
    # Test paper info
    print(f"\n📊 Testing paper info...")
    try:
        info = analyzer.get_paper_info()
        if 'error' not in info:
            print("✅ Paper info retrieved successfully")
            print(f"Chunks created: {info['processing_info']['chunks_created']}")
            print(f"File size: {info['file_stats']['size']}")
        else:
            print(f"❌ Paper info error: {info['error']}")
    except Exception as e:
        print(f"❌ Paper info retrieval failed: {e}")
    
    print(f"\n🎉 Testing completed!")

def create_sample_structure():
    """Create sample directory structure"""
    
    # Create directories
    os.makedirs("./sample_papers", exist_ok=True)
    os.makedirs("./uploads", exist_ok=True)
    os.makedirs("./vector_store", exist_ok=True)
    
    # Create a sample text file with instructions
    sample_instructions = """
# Sample Papers Directory

This directory should contain sample PDF research papers for testing the analyzer.

## How to add sample papers:

1. Download academic research papers in PDF format
2. Place them in this directory
3. Update the test script to use the correct filename

## Suggested sources for sample papers:

- arXiv.org (open access research papers)
- Google Scholar (look for PDF links)
- University repositories
- Conference proceedings

## Example papers to try:

- Machine Learning research papers
- Computer Science conference papers
- AI/NLP research articles
- Any academic paper in PDF format

Note: Make sure you have permission to use the papers for testing purposes.
"""
    
    with open("./sample_papers/README.txt", "w") as f:
        f.write(sample_instructions)
    
    print("📁 Sample directory structure created")
    print("📄 Please add sample PDF papers to ./sample_papers/ directory")

if __name__ == "__main__":
    # Create sample structure first
    create_sample_structure()
    
    # Run tests
    test_analyzer()
