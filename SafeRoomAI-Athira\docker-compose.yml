# docker-compose.yml
version: '3.8'

services:

  backend:
    build:
      context: .                          
      dockerfile: ./backend/Dockerfile
    container_name: saferoom-backend
    restart: unless-stopped
    env_file:
      - conf/.env
    volumes:
      - ./conf:/app/conf
      - ./backend/app:/app/app
      - ./backend/data/anomaly_screenshots:/app/data/anomaly_screenshots
      - ./backend/models:/app/models
    ports:
      - "8000:8000"
    environment:
      TZ: ${TZ}

  frontend:
    build:
      context: ./frontend               
      dockerfile: Dockerfile
    container_name: saferoom-frontend
    restart: unless-stopped
    depends_on:
      - backend
    env_file:
      - conf/.env
    ports:
      - "3000:3000"