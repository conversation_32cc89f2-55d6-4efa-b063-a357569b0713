import { MuiPickersAdapterContextValue } from '../LocalizationProvider/LocalizationProvider';
import { OnErrorProps, PickersTimezone, PickerValidDate } from '../models';
import type { PickerValueManager } from '../internals/hooks/usePicker';
export type Validator<TValue, TDate extends PickerValidDate, TError, TValidationProps> = {
    (params: {
        adapter: MuiPickersAdapterContextValue<TDate>;
        value: TValue;
        timezone: PickersTimezone;
        props: TValidationProps;
    }): TError;
    valueManager: PickerValueManager<TValue, TDate, any>;
};
interface UseValidationOptions<TValue, TDate extends PickerValidDate, TError, TValidationProps extends {}> extends OnErrorProps<TValue, TError> {
    /**
     * The value to validate.
     */
    value: TValue;
    /**
     * The timezone to use for the validation.
     */
    timezone: PickersTimezone;
    /**
     * The validator function to use.
     * They can be imported from `@mui/x-date-pickers/validation` and `@mui/x-date-pickers-pro/validation`.
     * It is recommended to only use the validator exported by the MUI X packages,
     * otherwise you may have inconsistent behaviors between the field and the views.
     */
    validator: Validator<TValue, TDate, TError, TValidationProps>;
    /**
     * The validation props, they differ depending on the component.
     * For example, the `validateTime` function supports `minTime`, `maxTime`, etc.
     */
    props: TValidationProps;
}
interface UseValidationReturnValue<TValue, TError> {
    /**
     * The validation error associated to the value passed to the `useValidation` hook.
     */
    validationError: TError;
    /**
     * `true` if the current error is not null.
     * For single value components, it means that the value is invalid.
     * For range components, it means that either start or end value is invalid.
     */
    hasValidationError: boolean;
    /**
     * Get the validation error for a new value.
     * This can be used to validate the value in a change handler before updating the state.
     * @template TValue The value type.
     * @param {TValue} newValue The value to validate.
     * @returns {TError} The validation error associated to the new value.
     */
    getValidationErrorForNewValue: (newValue: TValue) => TError;
}
/**
 * Utility hook to check if a given value is valid based on the provided validation props.
 * @template TDate
 * @template TValue The value type. It will be either the same type as `value` or `null`. It can be in `[start, end]` format in case of range value.
 * @template TError The validation error type. It will be either `string` or a `null`. It can be in `[start, end]` format in case of range value.
 * @param {UseValidationOptions<TValue, TDate, TError, TValidationProps>} options The options to configure the hook.
 * @param {TValue} options.value The value to validate.
 * @param {PickersTimezone} options.timezone The timezone to use for the validation.
 * @param {Validator<TValue, TDate, TError, TValidationProps>} options.validator The validator function to use.
 * @param {TValidationProps} options.props The validation props, they differ depending on the component.
 * @param {(error: TError, value: TValue) => void} options.onError Callback fired when the error associated with the current value changes.
 */
export declare function useValidation<TValue, TDate extends PickerValidDate, TError, TValidationProps extends {}>(options: UseValidationOptions<TValue, TDate, TError, TValidationProps>): UseValidationReturnValue<TValue, TError>;
export {};
