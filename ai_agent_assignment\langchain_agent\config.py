"""
Configuration settings for the Research Paper Analyzer Agent
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the Research Paper Analyzer"""
    
    # API Keys
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    
    # Model Settings
    LLM_MODEL = "gpt-3.5-turbo"
    EMBEDDING_MODEL = "text-embedding-ada-002"
    TEMPERATURE = 0.1  # Low temperature for consistent analysis
    MAX_TOKENS = 2000
    
    # Document Processing
    CHUNK_SIZE = 1000
    CHUNK_OVERLAP = 200
    MAX_DOCS_TO_RETRIEVE = 5
    
    # Vector Store Settings
    VECTOR_STORE_PATH = "./vector_store"
    PERSIST_DIRECTORY = "./chroma_db"
    
    # File Paths
    SAMPLE_PAPERS_DIR = "./sample_papers"
    UPLOAD_DIR = "./uploads"
    
    # Analysis Prompts
    SUMMARY_PROMPT = """
    You are an expert research analyst. Analyze the following research paper content and provide a comprehensive summary.
    
    Include:
    1. Main research question and objectives
    2. Methodology used
    3. Key findings and results
    4. Conclusions and implications
    5. Limitations mentioned by authors
    
    Content: {content}
    
    Summary:
    """
    
    RESEARCH_GAP_PROMPT = """
    You are an expert research analyst. Based on the following research paper content, identify potential research gaps and future research opportunities.
    
    Look for:
    1. Limitations mentioned by the authors
    2. Areas not fully explored
    3. Contradictory findings that need resolution
    4. Emerging trends that need investigation
    5. Methodological improvements needed
    
    Content: {content}
    
    Research Gaps and Opportunities:
    """
    
    CITATION_ANALYSIS_PROMPT = """
    You are an expert in academic writing. Analyze the citations in the following text and provide insights.
    
    Extract and analyze:
    1. Number of citations
    2. Types of sources (journals, conferences, books, etc.)
    3. Recency of citations
    4. Key authors or seminal works referenced
    5. Citation patterns and trends
    
    Text: {content}
    
    Citation Analysis:
    """
    
    QA_PROMPT = """
    You are a helpful research assistant. Answer the following question based on the provided research paper content.
    
    Be specific and cite relevant sections when possible. If the information is not available in the content, clearly state that.
    
    Question: {question}
    Content: {content}
    
    Answer:
    """

# Validation
if not Config.OPENAI_API_KEY:
    raise ValueError("OPENAI_API_KEY environment variable is required")

# Create necessary directories
os.makedirs(Config.UPLOAD_DIR, exist_ok=True)
os.makedirs(Config.SAMPLE_PAPERS_DIR, exist_ok=True)
os.makedirs(Config.VECTOR_STORE_PATH, exist_ok=True)
