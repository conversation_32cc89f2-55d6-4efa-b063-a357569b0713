# LlamaIndex Customer Support Knowledge Base Agent

## Overview
This agent uses LlamaIndex to create an intelligent customer support system that can answer questions based on product documentation, FAQs, and support knowledge base articles.

## Features
- **Document Indexing**: Automatically indexes product manuals, FAQs, and support documents
- **Semantic Search**: Uses vector embeddings for intelligent document retrieval
- **Context-Aware Responses**: Provides accurate answers based on indexed knowledge
- **Multi-Format Support**: Handles PDF, TXT, and Markdown documents
- **Real-time Updates**: Can add new documents to the knowledge base dynamically
- **Source Attribution**: Shows which documents were used to generate answers

## Architecture
```
Customer Support Agent
├── Document Loader (PDF, TXT, MD)
├── Text Splitter & Chunking
├── Vector Embeddings (OpenAI)
├── Vector Store (FAISS/Chroma)
├── Query Engine
├── Response Generator
└── Source Attribution
```

## Knowledge Base Structure
```
knowledge_base/
├── product_manuals/
│   ├── user_guide.pdf
│   ├── installation_manual.pdf
│   └── troubleshooting_guide.pdf
├── faqs/
│   ├── general_faq.md
│   ├── technical_faq.md
│   └── billing_faq.md
├── policies/
│   ├── return_policy.md
│   ├── privacy_policy.md
│   └── terms_of_service.md
└── support_articles/
    ├── common_issues.md
    ├── setup_guides.md
    └── advanced_features.md
```

## Core Components

### 1. Document Manager 📄
- **Purpose**: Load and manage knowledge base documents
- **Features**: Multi-format support, metadata extraction, document validation
- **Formats**: PDF, TXT, MD, DOCX

### 2. Index Builder 🔍
- **Purpose**: Create searchable indexes from documents
- **Features**: Vector embeddings, semantic chunking, index optimization
- **Technology**: LlamaIndex + OpenAI embeddings

### 3. Query Engine 🤖
- **Purpose**: Process customer queries and retrieve relevant information
- **Features**: Semantic search, context ranking, multi-document synthesis
- **Capabilities**: Natural language understanding, intent recognition

### 4. Response Generator 💬
- **Purpose**: Generate human-like responses with source attribution
- **Features**: Context-aware answers, source citations, confidence scoring
- **Output**: Structured responses with references

## Usage Examples

### Basic Query
```python
from customer_support_agent import CustomerSupportAgent

# Initialize agent
agent = CustomerSupportAgent()

# Load knowledge base
agent.load_knowledge_base("./knowledge_base")

# Ask a question
response = agent.ask("How do I reset my password?")
print(response['answer'])
print(f"Sources: {response['sources']}")
```

### Advanced Query with Context
```python
# Ask follow-up questions with context
response = agent.ask(
    "What are the system requirements?",
    context="I'm trying to install the software on Windows 11"
)
```

### Add New Document
```python
# Add new document to knowledge base
agent.add_document(
    file_path="./new_faq.pdf",
    category="faqs",
    metadata={"version": "2.0", "date": "2024-01-01"}
)
```

## Demo Scenarios

### 1. Product Information Queries
- "What are the system requirements for installation?"
- "How much does the premium plan cost?"
- "What features are included in the basic package?"

### 2. Technical Support
- "I'm getting an error message when starting the application"
- "How do I configure the database connection?"
- "The software crashes when I try to export data"

### 3. Account & Billing
- "How do I upgrade my subscription?"
- "What is your refund policy?"
- "I was charged twice this month"

### 4. Setup & Configuration
- "How do I install the software on macOS?"
- "What ports need to be open for the application?"
- "How do I configure SSL certificates?"

## Key Features

### Intelligent Document Processing
- Automatic text extraction from various formats
- Smart chunking based on document structure
- Metadata preservation and enhancement

### Advanced Search Capabilities
- Semantic similarity search using embeddings
- Hybrid search combining keyword and vector search
- Context-aware result ranking

### Response Quality
- Multi-document synthesis for comprehensive answers
- Source attribution with confidence scores
- Fallback responses for out-of-scope queries

### Knowledge Base Management
- Dynamic document addition and removal
- Index optimization and maintenance
- Version control for document updates

## Files
- `customer_support_agent.py`: Main LlamaIndex implementation
- `document_manager.py`: Document loading and processing
- `config.py`: Configuration settings
- `app.py`: Streamlit web interface
- `knowledge_base/`: Sample knowledge base documents
- `tests/`: Unit tests and integration tests

## Performance Metrics
- **Query Response Time**: < 2 seconds average
- **Accuracy**: 85%+ for in-scope queries
- **Source Attribution**: 95%+ accuracy
- **Index Build Time**: ~1 minute per 100 documents
