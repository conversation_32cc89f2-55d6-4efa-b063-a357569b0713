"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _propTypes = _interopRequireDefault(require("prop-types"));
var _composeClasses = _interopRequireDefault(require("@mui/utils/composeClasses"));
var _clsx = _interopRequireDefault(require("clsx"));
var _formControlState = _interopRequireDefault(require("../FormControl/formControlState"));
var _useFormControl = _interopRequireDefault(require("../FormControl/useFormControl"));
var _FormLabel = _interopRequireWildcard(require("../FormLabel"));
var _capitalize = _interopRequireDefault(require("../utils/capitalize"));
var _rootShouldForwardProp = _interopRequireDefault(require("../styles/rootShouldForwardProp"));
var _zeroStyled = require("../zero-styled");
var _memoTheme = _interopRequireDefault(require("../utils/memoTheme"));
var _DefaultPropsProvider = require("../DefaultPropsProvider");
var _inputLabelClasses = require("./inputLabelClasses");
var _jsxRuntime = require("react/jsx-runtime");
const useUtilityClasses = ownerState => {
  const {
    classes,
    formControl,
    size,
    shrink,
    disableAnimation,
    variant,
    required
  } = ownerState;
  const slots = {
    root: ['root', formControl && 'formControl', !disableAnimation && 'animated', shrink && 'shrink', size && size !== 'normal' && `size${(0, _capitalize.default)(size)}`, variant],
    asterisk: [required && 'asterisk']
  };
  const composedClasses = (0, _composeClasses.default)(slots, _inputLabelClasses.getInputLabelUtilityClasses, classes);
  return {
    ...classes,
    // forward the focused, disabled, etc. classes to the FormLabel
    ...composedClasses
  };
};
const InputLabelRoot = (0, _zeroStyled.styled)(_FormLabel.default, {
  shouldForwardProp: prop => (0, _rootShouldForwardProp.default)(prop) || prop === 'classes',
  name: 'MuiInputLabel',
  slot: 'Root',
  overridesResolver: (props, styles) => {
    const {
      ownerState
    } = props;
    return [{
      [`& .${_FormLabel.formLabelClasses.asterisk}`]: styles.asterisk
    }, styles.root, ownerState.formControl && styles.formControl, ownerState.size === 'small' && styles.sizeSmall, ownerState.shrink && styles.shrink, !ownerState.disableAnimation && styles.animated, ownerState.focused && styles.focused, styles[ownerState.variant]];
  }
})((0, _memoTheme.default)(({
  theme
}) => ({
  display: 'block',
  transformOrigin: 'top left',
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
  variants: [{
    props: ({
      ownerState
    }) => ownerState.formControl,
    style: {
      position: 'absolute',
      left: 0,
      top: 0,
      // slight alteration to spec spacing to match visual spec result
      transform: 'translate(0, 20px) scale(1)'
    }
  }, {
    props: {
      size: 'small'
    },
    style: {
      // Compensation for the `Input.inputSizeSmall` style.
      transform: 'translate(0, 17px) scale(1)'
    }
  }, {
    props: ({
      ownerState
    }) => ownerState.shrink,
    style: {
      transform: 'translate(0, -1.5px) scale(0.75)',
      transformOrigin: 'top left',
      maxWidth: '133%'
    }
  }, {
    props: ({
      ownerState
    }) => !ownerState.disableAnimation,
    style: {
      transition: theme.transitions.create(['color', 'transform', 'max-width'], {
        duration: theme.transitions.duration.shorter,
        easing: theme.transitions.easing.easeOut
      })
    }
  }, {
    props: {
      variant: 'filled'
    },
    style: {
      // Chrome's autofill feature gives the input field a yellow background.
      // Since the input field is behind the label in the HTML tree,
      // the input field is drawn last and hides the label with an opaque background color.
      // zIndex: 1 will raise the label above opaque background-colors of input.
      zIndex: 1,
      pointerEvents: 'none',
      transform: 'translate(12px, 16px) scale(1)',
      maxWidth: 'calc(100% - 24px)'
    }
  }, {
    props: {
      variant: 'filled',
      size: 'small'
    },
    style: {
      transform: 'translate(12px, 13px) scale(1)'
    }
  }, {
    props: ({
      variant,
      ownerState
    }) => variant === 'filled' && ownerState.shrink,
    style: {
      userSelect: 'none',
      pointerEvents: 'auto',
      transform: 'translate(12px, 7px) scale(0.75)',
      maxWidth: 'calc(133% - 24px)'
    }
  }, {
    props: ({
      variant,
      ownerState,
      size
    }) => variant === 'filled' && ownerState.shrink && size === 'small',
    style: {
      transform: 'translate(12px, 4px) scale(0.75)'
    }
  }, {
    props: {
      variant: 'outlined'
    },
    style: {
      // see comment above on filled.zIndex
      zIndex: 1,
      pointerEvents: 'none',
      transform: 'translate(14px, 16px) scale(1)',
      maxWidth: 'calc(100% - 24px)'
    }
  }, {
    props: {
      variant: 'outlined',
      size: 'small'
    },
    style: {
      transform: 'translate(14px, 9px) scale(1)'
    }
  }, {
    props: ({
      variant,
      ownerState
    }) => variant === 'outlined' && ownerState.shrink,
    style: {
      userSelect: 'none',
      pointerEvents: 'auto',
      // Theoretically, we should have (8+5)*2/0.75 = 34px
      // but it feels a better when it bleeds a bit on the left, so 32px.
      maxWidth: 'calc(133% - 32px)',
      transform: 'translate(14px, -9px) scale(0.75)'
    }
  }]
})));
const InputLabel = /*#__PURE__*/React.forwardRef(function InputLabel(inProps, ref) {
  const props = (0, _DefaultPropsProvider.useDefaultProps)({
    name: 'MuiInputLabel',
    props: inProps
  });
  const {
    disableAnimation = false,
    margin,
    shrink: shrinkProp,
    variant,
    className,
    ...other
  } = props;
  const muiFormControl = (0, _useFormControl.default)();
  let shrink = shrinkProp;
  if (typeof shrink === 'undefined' && muiFormControl) {
    shrink = muiFormControl.filled || muiFormControl.focused || muiFormControl.adornedStart;
  }
  const fcs = (0, _formControlState.default)({
    props,
    muiFormControl,
    states: ['size', 'variant', 'required', 'focused']
  });
  const ownerState = {
    ...props,
    disableAnimation,
    formControl: muiFormControl,
    shrink,
    size: fcs.size,
    variant: fcs.variant,
    required: fcs.required,
    focused: fcs.focused
  };
  const classes = useUtilityClasses(ownerState);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(InputLabelRoot, {
    "data-shrink": shrink,
    ref: ref,
    className: (0, _clsx.default)(classes.root, className),
    ...other,
    ownerState: ownerState,
    classes: classes
  });
});
process.env.NODE_ENV !== "production" ? InputLabel.propTypes /* remove-proptypes */ = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * The content of the component.
   */
  children: _propTypes.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: _propTypes.default.object,
  /**
   * @ignore
   */
  className: _propTypes.default.string,
  /**
   * The color of the component.
   * It supports both default and custom theme colors, which can be added as shown in the
   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).
   */
  color: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOfType([_propTypes.default.oneOf(['error', 'info', 'primary', 'secondary', 'success', 'warning']), _propTypes.default.string]),
  /**
   * If `true`, the transition animation is disabled.
   * @default false
   */
  disableAnimation: _propTypes.default.bool,
  /**
   * If `true`, the component is disabled.
   */
  disabled: _propTypes.default.bool,
  /**
   * If `true`, the label is displayed in an error state.
   */
  error: _propTypes.default.bool,
  /**
   * If `true`, the `input` of this label is focused.
   */
  focused: _propTypes.default.bool,
  /**
   * If `dense`, will adjust vertical spacing. This is normally obtained via context from
   * FormControl.
   */
  margin: _propTypes.default.oneOf(['dense']),
  /**
   * if `true`, the label will indicate that the `input` is required.
   */
  required: _propTypes.default.bool,
  /**
   * If `true`, the label is shrunk.
   */
  shrink: _propTypes.default.bool,
  /**
   * The size of the component.
   * @default 'normal'
   */
  size: _propTypes.default /* @typescript-to-proptypes-ignore */.oneOfType([_propTypes.default.oneOf(['normal', 'small']), _propTypes.default.string]),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: _propTypes.default.oneOfType([_propTypes.default.arrayOf(_propTypes.default.oneOfType([_propTypes.default.func, _propTypes.default.object, _propTypes.default.bool])), _propTypes.default.func, _propTypes.default.object]),
  /**
   * The variant to use.
   */
  variant: _propTypes.default.oneOf(['filled', 'outlined', 'standard'])
} : void 0;
var _default = exports.default = InputLabel;