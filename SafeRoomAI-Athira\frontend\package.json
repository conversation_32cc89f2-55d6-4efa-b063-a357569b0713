{"name": "frontend", "version": "0.1.0", "proxy": "http://localhost:8000", "private": true, "dependencies": {"@emotion/react": "^11.13.5", "@emotion/styled": "^11.13.5", "@mui/base": "^5.0.0-beta.70", "@mui/icons-material": "^6.3.0", "@mui/material": "^6.3.0", "@mui/x-charts": "^7.22.2", "@mui/x-date-pickers": "^7.22.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "react-scripts": "^5.0.1", "recharts": "^2.15.3", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8"}}