---
title: Overview
sidebar_label: Overview
pagination_next: null
pagination_prev: null
slug: /
---

`@typescript-eslint/eslint-plugin` includes over 100 rules that detect best practice violations, bugs, and/or stylistic issues specifically for TypeScript code.
See [Configs](/linting/configs) for how to enable recommended rules using configs.

## Supported Rules

import RulesTable from "@site/src/components/RulesTable";

<RulesTable ruleset="supported-rules" />

## Extension Rules

In some cases, ESLint provides a rule itself, but it doesn't support TypeScript syntax; either it crashes, or it ignores the syntax, or it falsely reports against it.
In these cases, we create what we call an extension rule; a rule within our plugin that has the same functionality, but also supports TypeScript.

<RulesTable ruleset="extension-rules" />
