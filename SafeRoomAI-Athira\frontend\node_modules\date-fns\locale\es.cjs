"use strict";
exports.es = void 0;
var _index = require("./es/_lib/formatDistance.cjs");
var _index2 = require("./es/_lib/formatLong.cjs");
var _index3 = require("./es/_lib/formatRelative.cjs");
var _index4 = require("./es/_lib/localize.cjs");
var _index5 = require("./es/_lib/match.cjs");

/**
 * @category Locales
 * @summary Spanish locale.
 * @language Spanish
 * @iso-639-2 spa
 * <AUTHOR> [@juanangosto](https://github.com/juanangosto)
 * <AUTHOR> [@guigrpa](https://github.com/guigrpa)
 * <AUTHOR> [@fjaguero](https://github.com/fjaguero)
 * <AUTHOR> [@harogaston](https://github.com/harogaston)
 * <AUTHOR> [@YagoCarballo](https://github.com/YagoCarballo)
 */
const es = (exports.es = {
  code: "es",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
});
