import type { ResponsiveChartContainerProps } from './ResponsiveChartContainer';
/**
 * Wrapping div that take the shape of its parent.
 *
 * @ignore - do not document.
 */
export declare const ResizableContainer: import("@emotion/styled").StyledComponent<import("@mui/system").MUIStyledCommonProps<import("@mui/material").Theme> & {
    ownerState: Pick<ResponsiveChartContainerProps, "width" | "height">;
}, Pick<import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLDivElement>, HTMLDivElement>, keyof import("react").ClassAttributes<HTMLDivElement> | keyof import("react").HTMLAttributes<HTMLDivElement>>, {}>;
