import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link formatRFC3339} function options.
 */
export interface FormatRFC3339Options extends ContextOptions<Date> {
  /** The number of digits after the decimal point after seconds, defaults to 0 */
  fractionDigits?: 0 | 1 | 2 | 3;
}
/**
 * @name formatRFC3339
 * @category Common Helpers
 * @summary Format the date according to the RFC 3339 standard (https://tools.ietf.org/html/rfc3339#section-5.6).
 *
 * @description
 * Return the formatted date string in RFC 3339 format. Options may be passed to control the parts and notations of the date.
 *
 * @param date - The original date
 * @param options - An object with options.
 *
 * @returns The formatted date string
 *
 * @throws `date` must not be Invalid Date
 *
 * @example
 * // Represent 18 September 2019 in RFC 3339 format:
 * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52))
 * //=> '2019-09-18T19:00:52Z'
 *
 * @example
 * // Represent 18 September 2019 in RFC 3339 format, 3 digits of second fraction
 * formatRFC3339(new Date(2019, 8, 18, 19, 0, 52, 234), {
 *   fractionDigits: 3
 * })
 * //=> '2019-09-18T19:00:52.234Z'
 */
export declare function formatRFC3339(
  date: DateArg<Date> & {},
  options?: FormatRFC3339Options,
): string;
