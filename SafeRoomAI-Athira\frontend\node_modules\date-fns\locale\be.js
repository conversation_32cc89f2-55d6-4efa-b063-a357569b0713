import { formatDistance } from "./be/_lib/formatDistance.js";
import { formatLong } from "./be/_lib/formatLong.js";
import { formatRelative } from "./be/_lib/formatRelative.js";
import { localize } from "./be/_lib/localize.js";
import { match } from "./be/_lib/match.js";

/**
 * @category Locales
 * @summary Belarusian locale.
 * @language Belarusian
 * @iso-639-2 bel
 * <AUTHOR> [@alyrik](https://github.com/alyrik)
 * <AUTHOR> [@arvigeus](https://github.com/mawi12345)
 */
export const be = {
  code: "be",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 1,
  },
};

// Fallback for modularized imports:
export default be;
