"""
Sample code with performance issues for testing the AutoGen Code Review Team
"""

import time
import requests
from typing import List, Dict

def inefficient_search(data: List[Dict], target_value: str) -> List[Dict]:
    """
    PERFORMANCE ISSUE: O(n²) search algorithm
    Nested loops create quadratic time complexity
    """
    results = []
    
    for item in data:
        for key, value in item.items():
            if str(value).lower() == target_value.lower():
                # PERFORMANCE ISSUE: Duplicate checking
                duplicate_found = False
                for existing in results:
                    if existing == item:
                        duplicate_found = True
                        break
                
                if not duplicate_found:
                    results.append(item)
    
    return results

def process_large_dataset(dataset: List[int]) -> List[int]:
    """
    PERFORMANCE ISSUE: Inefficient list operations
    Multiple passes through data and inefficient sorting
    """
    # PERFORMANCE ISSUE: Creating new lists in each iteration
    processed_data = []
    
    for item in dataset:
        # PERFORMANCE ISSUE: Expensive operation in loop
        time.sleep(0.001)  # Simulating slow operation
        
        if item > 0:
            processed_data.append(item * 2)
    
    # PERFORMANCE ISSUE: Bubble sort implementation
    for i in range(len(processed_data)):
        for j in range(len(processed_data) - 1):
            if processed_data[j] > processed_data[j + 1]:
                processed_data[j], processed_data[j + 1] = processed_data[j + 1], processed_data[j]
    
    return processed_data

def fetch_user_data(user_ids: List[int]) -> Dict[int, Dict]:
    """
    PERFORMANCE ISSUE: N+1 query problem
    Making individual API calls instead of batch requests
    """
    user_data = {}
    
    for user_id in user_ids:
        # PERFORMANCE ISSUE: Individual API calls
        response = requests.get(f"https://api.example.com/users/{user_id}")
        
        if response.status_code == 200:
            user_data[user_id] = response.json()
        
        # PERFORMANCE ISSUE: No connection pooling or session reuse
        time.sleep(0.1)  # Simulating network delay
    
    return user_data

def calculate_statistics(numbers: List[float]) -> Dict[str, float]:
    """
    PERFORMANCE ISSUE: Redundant calculations
    Multiple passes through the same data
    """
    # PERFORMANCE ISSUE: Multiple iterations over the same data
    total = 0
    for num in numbers:
        total += num
    
    count = 0
    for num in numbers:
        count += 1
    
    mean = total / count
    
    # PERFORMANCE ISSUE: Inefficient variance calculation
    variance_sum = 0
    for num in numbers:
        variance_sum += (num - mean) ** 2
    
    variance = variance_sum / count
    
    # PERFORMANCE ISSUE: Inefficient min/max finding
    minimum = numbers[0]
    for num in numbers:
        if num < minimum:
            minimum = num
    
    maximum = numbers[0]
    for num in numbers:
        if num > maximum:
            maximum = num
    
    return {
        'mean': mean,
        'variance': variance,
        'min': minimum,
        'max': maximum,
        'count': count
    }

class DataProcessor:
    """
    PERFORMANCE ISSUE: Inefficient data structure usage
    Using lists where sets or dictionaries would be more appropriate
    """
    
    def __init__(self):
        self.processed_items = []  # Should use set for O(1) lookups
        self.cache = []  # Should use dictionary for O(1) access
    
    def process_item(self, item: str) -> bool:
        """
        PERFORMANCE ISSUE: O(n) lookup in list
        Should use set for constant time lookup
        """
        # PERFORMANCE ISSUE: Linear search in list
        if item in self.processed_items:
            return False
        
        # Simulate processing
        processed_item = item.upper()
        self.processed_items.append(processed_item)
        
        return True
    
    def get_cached_result(self, key: str) -> str:
        """
        PERFORMANCE ISSUE: Linear search in cache
        Should use dictionary for O(1) access
        """
        for cached_item in self.cache:
            if cached_item['key'] == key:
                return cached_item['value']
        
        return None
    
    def add_to_cache(self, key: str, value: str):
        """
        PERFORMANCE ISSUE: No cache size limit
        Memory usage can grow indefinitely
        """
        self.cache.append({'key': key, 'value': value})

def generate_report(data: List[Dict]) -> str:
    """
    PERFORMANCE ISSUE: String concatenation in loop
    Creates new string objects in each iteration
    """
    report = ""
    
    for item in data:
        # PERFORMANCE ISSUE: String concatenation creates new objects
        report += f"Item ID: {item.get('id', 'N/A')}\n"
        report += f"Name: {item.get('name', 'N/A')}\n"
        report += f"Value: {item.get('value', 'N/A')}\n"
        report += "---\n"
    
    return report

def find_duplicates(items: List[str]) -> List[str]:
    """
    PERFORMANCE ISSUE: O(n²) duplicate detection
    Nested loops for finding duplicates
    """
    duplicates = []
    
    for i in range(len(items)):
        for j in range(i + 1, len(items)):
            if items[i] == items[j]:
                # PERFORMANCE ISSUE: Linear search to check if already added
                if items[i] not in duplicates:
                    duplicates.append(items[i])
    
    return duplicates

def recursive_fibonacci(n: int) -> int:
    """
    PERFORMANCE ISSUE: Exponential time complexity
    No memoization for recursive calls
    """
    if n <= 1:
        return n
    
    # PERFORMANCE ISSUE: Redundant recursive calls
    return recursive_fibonacci(n - 1) + recursive_fibonacci(n - 2)

# PERFORMANCE ISSUE: Global variables and inefficient initialization
LARGE_GLOBAL_LIST = list(range(1000000))  # Large global data structure
GLOBAL_CACHE = {}

def inefficient_main():
    """
    PERFORMANCE ISSUE: Inefficient main function
    Multiple performance anti-patterns
    """
    # PERFORMANCE ISSUE: Processing large dataset inefficiently
    large_dataset = list(range(10000))
    result = process_large_dataset(large_dataset)
    
    # PERFORMANCE ISSUE: Unnecessary file I/O in loop
    for i in range(100):
        with open(f"temp_file_{i}.txt", "w") as f:
            f.write(f"Data: {i}")
    
    # PERFORMANCE ISSUE: Memory-intensive operations
    memory_intensive_data = []
    for i in range(100000):
        memory_intensive_data.append([0] * 1000)
    
    return result

if __name__ == "__main__":
    inefficient_main()
