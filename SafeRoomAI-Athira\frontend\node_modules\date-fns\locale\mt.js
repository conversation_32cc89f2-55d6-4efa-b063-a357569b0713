import { formatDistance } from "./mt/_lib/formatDistance.js";
import { formatLong } from "./mt/_lib/formatLong.js";
import { formatRelative } from "./mt/_lib/formatRelative.js";
import { localize } from "./mt/_lib/localize.js";
import { match } from "./mt/_lib/match.js";

/**
 * @category Locales
 * @summary Maltese locale.
 * @language Maltese
 * @iso-639-2 mlt
 * <AUTHOR> [@amatzon](@link https://github.com/amatzon)
 * <AUTHOR> [@bryanMt](@link https://github.com/bryanMt)
 */
export const mt = {
  code: "mt",
  formatDistance: formatDistance,
  formatLong: formatLong,
  formatRelative: formatRelative,
  localize: localize,
  match: match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
};

// Fallback for modularized imports:
export default mt;
