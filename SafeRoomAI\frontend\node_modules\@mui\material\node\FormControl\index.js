"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
var _exportNames = {
  useFormControl: true,
  formControlClasses: true
};
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _FormControl.default;
  }
});
Object.defineProperty(exports, "formControlClasses", {
  enumerable: true,
  get: function () {
    return _formControlClasses.default;
  }
});
Object.defineProperty(exports, "useFormControl", {
  enumerable: true,
  get: function () {
    return _useFormControl.default;
  }
});
var _FormControl = _interopRequireDefault(require("./FormControl"));
var _useFormControl = _interopRequireDefault(require("./useFormControl"));
var _formControlClasses = _interopRequireWildcard(require("./formControlClasses"));
Object.keys(_formControlClasses).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (Object.prototype.hasOwnProperty.call(_exportNames, key)) return;
  if (key in exports && exports[key] === _formControlClasses[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _formControlClasses[key];
    }
  });
});