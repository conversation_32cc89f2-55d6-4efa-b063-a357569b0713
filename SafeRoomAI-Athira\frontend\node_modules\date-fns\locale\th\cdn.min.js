(()=>{var A;function B(C){return B=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(G){return typeof G}:function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},B(C)}function x(C,G){var H=Object.keys(C);if(Object.getOwnPropertySymbols){var J=Object.getOwnPropertySymbols(C);G&&(J=J.filter(function(X){return Object.getOwnPropertyDescriptor(C,X).enumerable})),H.push.apply(H,J)}return H}function Q(C){for(var G=1;G<arguments.length;G++){var H=arguments[G]!=null?arguments[G]:{};G%2?x(Object(H),!0).forEach(function(J){E(C,J,H[J])}):Object.getOwnPropertyDescriptors?Object.defineProperties(C,Object.getOwnPropertyDescriptors(H)):x(Object(H)).forEach(function(J){Object.defineProperty(C,J,Object.getOwnPropertyDescriptor(H,J))})}return C}function E(C,G,H){if(G=N(G),G in C)Object.defineProperty(C,G,{value:H,enumerable:!0,configurable:!0,writable:!0});else C[G]=H;return C}function N(C){var G=z(C,"string");return B(G)=="symbol"?G:String(G)}function z(C,G){if(B(C)!="object"||!C)return C;var H=C[Symbol.toPrimitive];if(H!==void 0){var J=H.call(C,G||"default");if(B(J)!="object")return J;throw new TypeError("@@toPrimitive must return a primitive value.")}return(G==="string"?String:Number)(C)}var W=Object.defineProperty,HC=function C(G,H){for(var J in H)W(G,J,{get:H[J],enumerable:!0,configurable:!0,set:function X(Y){return H[J]=function(){return Y}}})},D={lessThanXSeconds:{one:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 1 \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35",other:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 {{count}} \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"},xSeconds:{one:"1 \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35",other:"{{count}} \u0E27\u0E34\u0E19\u0E32\u0E17\u0E35"},halfAMinute:"\u0E04\u0E23\u0E36\u0E48\u0E07\u0E19\u0E32\u0E17\u0E35",lessThanXMinutes:{one:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 1 \u0E19\u0E32\u0E17\u0E35",other:"\u0E19\u0E49\u0E2D\u0E22\u0E01\u0E27\u0E48\u0E32 {{count}} \u0E19\u0E32\u0E17\u0E35"},xMinutes:{one:"1 \u0E19\u0E32\u0E17\u0E35",other:"{{count}} \u0E19\u0E32\u0E17\u0E35"},aboutXHours:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07"},xHours:{one:"1 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07",other:"{{count}} \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07"},xDays:{one:"1 \u0E27\u0E31\u0E19",other:"{{count}} \u0E27\u0E31\u0E19"},aboutXWeeks:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C"},xWeeks:{one:"1 \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C",other:"{{count}} \u0E2A\u0E31\u0E1B\u0E14\u0E32\u0E2B\u0E4C"},aboutXMonths:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E40\u0E14\u0E37\u0E2D\u0E19",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E40\u0E14\u0E37\u0E2D\u0E19"},xMonths:{one:"1 \u0E40\u0E14\u0E37\u0E2D\u0E19",other:"{{count}} \u0E40\u0E14\u0E37\u0E2D\u0E19"},aboutXYears:{one:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 1 \u0E1B\u0E35",other:"\u0E1B\u0E23\u0E30\u0E21\u0E32\u0E13 {{count}} \u0E1B\u0E35"},xYears:{one:"1 \u0E1B\u0E35",other:"{{count}} \u0E1B\u0E35"},overXYears:{one:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32 1 \u0E1B\u0E35",other:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32 {{count}} \u0E1B\u0E35"},almostXYears:{one:"\u0E40\u0E01\u0E37\u0E2D\u0E1A 1 \u0E1B\u0E35",other:"\u0E40\u0E01\u0E37\u0E2D\u0E1A {{count}} \u0E1B\u0E35"}},S=function C(G,H,J){var X,Y=D[G];if(typeof Y==="string")X=Y;else if(H===1)X=Y.one;else X=Y.other.replace("{{count}}",String(H));if(J!==null&&J!==void 0&&J.addSuffix)if(J.comparison&&J.comparison>0)if(G==="halfAMinute")return"\u0E43\u0E19"+X;else return"\u0E43\u0E19 "+X;else return X+"\u0E17\u0E35\u0E48\u0E1C\u0E48\u0E32\u0E19\u0E21\u0E32";return X};function $(C){return function(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.width?String(G.width):C.defaultWidth,J=C.formats[H]||C.formats[C.defaultWidth];return J}}var M={full:"\u0E27\u0E31\u0E19EEEE\u0E17\u0E35\u0E48 do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"dd/MM/yyyy"},R={full:"H:mm:ss \u0E19. zzzz",long:"H:mm:ss \u0E19. z",medium:"H:mm:ss \u0E19.",short:"H:mm \u0E19."},L={full:"{{date}} '\u0E40\u0E27\u0E25\u0E32' {{time}}",long:"{{date}} '\u0E40\u0E27\u0E25\u0E32' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},V={date:$({formats:M,defaultWidth:"full"}),time:$({formats:R,defaultWidth:"medium"}),dateTime:$({formats:L,defaultWidth:"full"})},j={lastWeek:"eeee'\u0E17\u0E35\u0E48\u0E41\u0E25\u0E49\u0E27\u0E40\u0E27\u0E25\u0E32' p",yesterday:"'\u0E40\u0E21\u0E37\u0E48\u0E2D\u0E27\u0E32\u0E19\u0E19\u0E35\u0E49\u0E40\u0E27\u0E25\u0E32' p",today:"'\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49\u0E40\u0E27\u0E25\u0E32' p",tomorrow:"'\u0E1E\u0E23\u0E38\u0E48\u0E07\u0E19\u0E35\u0E49\u0E40\u0E27\u0E25\u0E32' p",nextWeek:"eeee '\u0E40\u0E27\u0E25\u0E32' p",other:"P"},w=function C(G,H,J,X){return j[G]};function I(C){return function(G,H){var J=H!==null&&H!==void 0&&H.context?String(H.context):"standalone",X;if(J==="formatting"&&C.formattingValues){var Y=C.defaultFormattingWidth||C.defaultWidth,Z=H!==null&&H!==void 0&&H.width?String(H.width):Y;X=C.formattingValues[Z]||C.formattingValues[Y]}else{var T=C.defaultWidth,q=H!==null&&H!==void 0&&H.width?String(H.width):C.defaultWidth;X=C.values[q]||C.values[T]}var U=C.argumentCallback?C.argumentCallback(G):G;return X[U]}}var _={narrow:["B","\u0E04\u0E28"],abbreviated:["BC","\u0E04.\u0E28."],wide:["\u0E1B\u0E35\u0E01\u0E48\u0E2D\u0E19\u0E04\u0E23\u0E34\u0E2A\u0E15\u0E01\u0E32\u0E25","\u0E04\u0E23\u0E34\u0E2A\u0E15\u0E4C\u0E28\u0E31\u0E01\u0E23\u0E32\u0E0A"]},f={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E41\u0E23\u0E01","\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E17\u0E35\u0E48\u0E2A\u0E2D\u0E07","\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E17\u0E35\u0E48\u0E2A\u0E32\u0E21","\u0E44\u0E15\u0E23\u0E21\u0E32\u0E2A\u0E17\u0E35\u0E48\u0E2A\u0E35\u0E48"]},F={narrow:["\u0E2D\u0E32.","\u0E08.","\u0E2D.","\u0E1E.","\u0E1E\u0E24.","\u0E28.","\u0E2A."],short:["\u0E2D\u0E32.","\u0E08.","\u0E2D.","\u0E1E.","\u0E1E\u0E24.","\u0E28.","\u0E2A."],abbreviated:["\u0E2D\u0E32.","\u0E08.","\u0E2D.","\u0E1E.","\u0E1E\u0E24.","\u0E28.","\u0E2A."],wide:["\u0E2D\u0E32\u0E17\u0E34\u0E15\u0E22\u0E4C","\u0E08\u0E31\u0E19\u0E17\u0E23\u0E4C","\u0E2D\u0E31\u0E07\u0E04\u0E32\u0E23","\u0E1E\u0E38\u0E18","\u0E1E\u0E24\u0E2B\u0E31\u0E2A\u0E1A\u0E14\u0E35","\u0E28\u0E38\u0E01\u0E23\u0E4C","\u0E40\u0E2A\u0E32\u0E23\u0E4C"]},v={narrow:["\u0E21.\u0E04.","\u0E01.\u0E1E.","\u0E21\u0E35.\u0E04.","\u0E40\u0E21.\u0E22.","\u0E1E.\u0E04.","\u0E21\u0E34.\u0E22.","\u0E01.\u0E04.","\u0E2A.\u0E04.","\u0E01.\u0E22.","\u0E15.\u0E04.","\u0E1E.\u0E22.","\u0E18.\u0E04."],abbreviated:["\u0E21.\u0E04.","\u0E01.\u0E1E.","\u0E21\u0E35.\u0E04.","\u0E40\u0E21.\u0E22.","\u0E1E.\u0E04.","\u0E21\u0E34.\u0E22.","\u0E01.\u0E04.","\u0E2A.\u0E04.","\u0E01.\u0E22.","\u0E15.\u0E04.","\u0E1E.\u0E22.","\u0E18.\u0E04."],wide:["\u0E21\u0E01\u0E23\u0E32\u0E04\u0E21","\u0E01\u0E38\u0E21\u0E20\u0E32\u0E1E\u0E31\u0E19\u0E18\u0E4C","\u0E21\u0E35\u0E19\u0E32\u0E04\u0E21","\u0E40\u0E21\u0E29\u0E32\u0E22\u0E19","\u0E1E\u0E24\u0E29\u0E20\u0E32\u0E04\u0E21","\u0E21\u0E34\u0E16\u0E38\u0E19\u0E32\u0E22\u0E19","\u0E01\u0E23\u0E01\u0E0E\u0E32\u0E04\u0E21","\u0E2A\u0E34\u0E07\u0E2B\u0E32\u0E04\u0E21","\u0E01\u0E31\u0E19\u0E22\u0E32\u0E22\u0E19","\u0E15\u0E38\u0E25\u0E32\u0E04\u0E21","\u0E1E\u0E24\u0E28\u0E08\u0E34\u0E01\u0E32\u0E22\u0E19","\u0E18\u0E31\u0E19\u0E27\u0E32\u0E04\u0E21"]},P={narrow:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E1A\u0E48\u0E32\u0E22",evening:"\u0E40\u0E22\u0E47\u0E19",night:"\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},abbreviated:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E1A\u0E48\u0E32\u0E22",evening:"\u0E40\u0E22\u0E47\u0E19",night:"\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},wide:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E1A\u0E48\u0E32\u0E22",evening:"\u0E40\u0E22\u0E47\u0E19",night:"\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"}},k={narrow:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E15\u0E2D\u0E19\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",evening:"\u0E15\u0E2D\u0E19\u0E40\u0E22\u0E47\u0E19",night:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},abbreviated:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E15\u0E2D\u0E19\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",evening:"\u0E15\u0E2D\u0E19\u0E40\u0E22\u0E47\u0E19",night:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"},wide:{am:"\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",pm:"\u0E2B\u0E25\u0E31\u0E07\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",midnight:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07\u0E04\u0E37\u0E19",noon:"\u0E40\u0E17\u0E35\u0E48\u0E22\u0E07",morning:"\u0E15\u0E2D\u0E19\u0E40\u0E0A\u0E49\u0E32",afternoon:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E27\u0E31\u0E19",evening:"\u0E15\u0E2D\u0E19\u0E40\u0E22\u0E47\u0E19",night:"\u0E15\u0E2D\u0E19\u0E01\u0E25\u0E32\u0E07\u0E04\u0E37\u0E19"}},b=function C(G,H){return String(G)},h={ordinalNumber:b,era:I({values:_,defaultWidth:"wide"}),quarter:I({values:f,defaultWidth:"wide",argumentCallback:function C(G){return G-1}}),month:I({values:v,defaultWidth:"wide"}),day:I({values:F,defaultWidth:"wide"}),dayPeriod:I({values:P,defaultWidth:"wide",formattingValues:k,defaultFormattingWidth:"wide"})};function O(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=H.width,X=J&&C.matchPatterns[J]||C.matchPatterns[C.defaultMatchWidth],Y=G.match(X);if(!Y)return null;var Z=Y[0],T=J&&C.parsePatterns[J]||C.parsePatterns[C.defaultParseWidth],q=Array.isArray(T)?c(T,function(K){return K.test(Z)}):m(T,function(K){return K.test(Z)}),U;U=C.valueCallback?C.valueCallback(q):q,U=H.valueCallback?H.valueCallback(U):U;var GC=G.slice(Z.length);return{value:U,rest:GC}}}function m(C,G){for(var H in C)if(Object.prototype.hasOwnProperty.call(C,H)&&G(C[H]))return H;return}function c(C,G){for(var H=0;H<C.length;H++)if(G(C[H]))return H;return}function y(C){return function(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},J=G.match(C.matchPattern);if(!J)return null;var X=J[0],Y=G.match(C.parsePattern);if(!Y)return null;var Z=C.valueCallback?C.valueCallback(Y[0]):Y[0];Z=H.valueCallback?H.valueCallback(Z):Z;var T=G.slice(X.length);return{value:Z,rest:T}}}var p=/^\d+/i,d=/\d+/i,g={narrow:/^([bB]|[aA]|คศ)/i,abbreviated:/^([bB]\.?\s?[cC]\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?|ค\.?ศ\.?)/i,wide:/^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i},u={any:[/^[bB]/i,/^(^[aA]|ค\.?ศ\.?|คริสตกาล|คริสต์ศักราช|)/i]},l={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^ไตรมาส(ที่)? ?[1234]/i},i={any:[/(1|แรก|หนึ่ง)/i,/(2|สอง)/i,/(3|สาม)/i,/(4|สี่)/i]},n={narrow:/^(ม\.?ค\.?|ก\.?พ\.?|มี\.?ค\.?|เม\.?ย\.?|พ\.?ค\.?|มิ\.?ย\.?|ก\.?ค\.?|ส\.?ค\.?|ก\.?ย\.?|ต\.?ค\.?|พ\.?ย\.?|ธ\.?ค\.?)/i,abbreviated:/^(ม\.?ค\.?|ก\.?พ\.?|มี\.?ค\.?|เม\.?ย\.?|พ\.?ค\.?|มิ\.?ย\.?|ก\.?ค\.?|ส\.?ค\.?|ก\.?ย\.?|ต\.?ค\.?|พ\.?ย\.?|ธ\.?ค\.?')/i,wide:/^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i},s={wide:[/^มก/i,/^กุม/i,/^มี/i,/^เม/i,/^พฤษ/i,/^มิ/i,/^กรก/i,/^ส/i,/^กัน/i,/^ต/i,/^พฤศ/i,/^ธ/i],any:[/^ม\.?ค\.?/i,/^ก\.?พ\.?/i,/^มี\.?ค\.?/i,/^เม\.?ย\.?/i,/^พ\.?ค\.?/i,/^มิ\.?ย\.?/i,/^ก\.?ค\.?/i,/^ส\.?ค\.?/i,/^ก\.?ย\.?/i,/^ต\.?ค\.?/i,/^พ\.?ย\.?/i,/^ธ\.?ค\.?/i]},o={narrow:/^(อา\.?|จ\.?|อ\.?|พฤ\.?|พ\.?|ศ\.?|ส\.?)/i,short:/^(อา\.?|จ\.?|อ\.?|พฤ\.?|พ\.?|ศ\.?|ส\.?)/i,abbreviated:/^(อา\.?|จ\.?|อ\.?|พฤ\.?|พ\.?|ศ\.?|ส\.?)/i,wide:/^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i},r={wide:[/^อา/i,/^จั/i,/^อั/i,/^พุธ/i,/^พฤ/i,/^ศ/i,/^เส/i],any:[/^อา/i,/^จ/i,/^อ/i,/^พ(?!ฤ)/i,/^พฤ/i,/^ศ/i,/^ส/i]},a={any:/^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i},e={any:{am:/^ก่อนเที่ยง/i,pm:/^หลังเที่ยง/i,midnight:/^เที่ยงคืน/i,noon:/^เที่ยง/i,morning:/เช้า/i,afternoon:/บ่าย/i,evening:/เย็น/i,night:/กลางคืน/i}},t={ordinalNumber:y({matchPattern:p,parsePattern:d,valueCallback:function C(G){return parseInt(G,10)}}),era:O({matchPatterns:g,defaultMatchWidth:"wide",parsePatterns:u,defaultParseWidth:"any"}),quarter:O({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any",valueCallback:function C(G){return G+1}}),month:O({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),day:O({matchPatterns:o,defaultMatchWidth:"wide",parsePatterns:r,defaultParseWidth:"any"}),dayPeriod:O({matchPatterns:a,defaultMatchWidth:"any",parsePatterns:e,defaultParseWidth:"any"})},CC={code:"th",formatDistance:S,formatLong:V,formatRelative:w,localize:h,match:t,options:{weekStartsOn:0,firstWeekContainsDate:1}};window.dateFns=Q(Q({},window.dateFns),{},{locale:Q(Q({},(A=window.dateFns)===null||A===void 0?void 0:A.locale),{},{th:CC})})})();

//# debugId=FC2AF64C0CAE8B0464756E2164756E21
