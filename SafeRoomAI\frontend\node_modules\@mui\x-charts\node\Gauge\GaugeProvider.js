"use strict";
'use client';

// @ignore - do not document.
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.GaugeContext = void 0;
exports.GaugeProvider = GaugeProvider;
exports.useGaugeState = useGaugeState;
var React = _interopRequireWildcard(require("react"));
var _getPercentageValue = require("../internals/getPercentageValue");
var _utils = require("./utils");
var _useDrawingArea = require("../hooks/useDrawingArea");
var _jsxRuntime = require("react/jsx-runtime");
const GaugeContext = exports.GaugeContext = /*#__PURE__*/React.createContext({
  value: null,
  valueMin: 0,
  valueMax: 0,
  startAngle: 0,
  endAngle: 0,
  innerRadius: 0,
  outerRadius: 0,
  cornerRadius: 0,
  cx: 0,
  cy: 0,
  maxRadius: 0,
  valueAngle: null
});
if (process.env.NODE_ENV !== 'production') {
  GaugeContext.displayName = 'GaugeContext';
}
function GaugeProvider(props) {
  const {
    value = null,
    valueMin = 0,
    valueMax = 100,
    startAngle = 0,
    endAngle = 360,
    outerRadius: outerRadiusParam,
    innerRadius: innerRadiusParam,
    cornerRadius: cornerRadiusParam,
    cx: cxParam,
    cy: cyParam,
    children
  } = props;
  const {
    left,
    top,
    width,
    height
  } = (0, _useDrawingArea.useDrawingArea)();
  const ratios = (0, _utils.getArcRatios)(startAngle, endAngle);
  const innerCx = cxParam ? (0, _getPercentageValue.getPercentageValue)(cxParam, width) : ratios.cx * width;
  const innerCy = cyParam ? (0, _getPercentageValue.getPercentageValue)(cyParam, height) : ratios.cy * height;
  let cx = left + innerCx;
  let cy = top + innerCy;
  const maxRadius = (0, _utils.getAvailableRadius)(innerCx, innerCy, width, height, ratios);

  // If the center is not defined, after computation of the available radius, update the center to use the remaining space.
  if (cxParam === undefined) {
    const usedWidth = maxRadius * (ratios.maxX - ratios.minX);
    cx = left + (width - usedWidth) / 2 + ratios.cx * usedWidth;
  }
  if (cyParam === undefined) {
    const usedHeight = maxRadius * (ratios.maxY - ratios.minY);
    cy = top + (height - usedHeight) / 2 + ratios.cy * usedHeight;
  }
  const outerRadius = (0, _getPercentageValue.getPercentageValue)(outerRadiusParam ?? maxRadius, maxRadius);
  const innerRadius = (0, _getPercentageValue.getPercentageValue)(innerRadiusParam ?? '80%', maxRadius);
  const cornerRadius = (0, _getPercentageValue.getPercentageValue)(cornerRadiusParam ?? 0, outerRadius - innerRadius);
  const contextValue = React.useMemo(() => {
    const startAngleRad = Math.PI * startAngle / 180;
    const endAngleRad = Math.PI * endAngle / 180;
    return {
      value,
      valueMin,
      valueMax,
      startAngle: startAngleRad,
      endAngle: endAngleRad,
      outerRadius,
      innerRadius,
      cornerRadius,
      cx,
      cy,
      maxRadius,
      valueAngle: value === null ? null : startAngleRad + (endAngleRad - startAngleRad) * (value - valueMin) / (valueMax - valueMin)
    };
  }, [value, valueMin, valueMax, startAngle, endAngle, outerRadius, innerRadius, cornerRadius, cx, cy, maxRadius]);
  return /*#__PURE__*/(0, _jsxRuntime.jsx)(GaugeContext.Provider, {
    value: contextValue,
    children: children
  });
}
function useGaugeState() {
  return React.useContext(GaugeContext);
}