"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "14",
  cy: "6",
  r: "1"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "m13.8 11.48.2.02c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5-1.5.67-1.5 1.5l.02.2c.09.67.61 1.19 1.28 1.28M14 3.5c.28 0 .5-.22.5-.5s-.22-.5-.5-.5-.5.22-.5.5.22.5.5.5m-4 0c.28 0 .5-.22.5-.5s-.22-.5-.5-.5-.5.22-.5.5.22.5.5.5"
}, "1"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "18",
  cy: "10",
  r: "1"
}, "2"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "18",
  cy: "6",
  r: "1"
}, "3"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M21 10.5c.28 0 .5-.22.5-.5s-.22-.5-.5-.5-.5.22-.5.5.22.5.5.5"
}, "4"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "6",
  r: "1"
}, "5"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "18",
  cy: "14",
  r: "1"
}, "6"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "6",
  cy: "18",
  r: "1"
}, "7"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M14 20.5c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5m7-7c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5m-18 0c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5"
}, "8"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "10",
  cy: "18",
  r: "1"
}, "9"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M3 9.5c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5m7 11c-.28 0-.5.22-.5.5s.22.5.5.5.5-.22.5-.5-.22-.5-.5-.5"
}, "10"), /*#__PURE__*/(0, _jsxRuntime.jsx)("circle", {
  cx: "6",
  cy: "14",
  r: "1"
}, "11"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M2.5 5.27 6 8.77l.28.28L6 9c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1c0-.1-.03-.19-.06-.28l2.81 2.81c-.71.11-1.25.73-1.25 1.47 0 .83.67 1.5 1.5 1.5.74 0 1.36-.54 1.47-1.25l2.81 2.81c-.09-.03-.18-.06-.28-.06-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1c0-.1-.03-.19-.06-.28l3.78 3.78h.01l1.41-1.41L3.91 3.86z"
}, "12")], 'BlurOffTwoTone');